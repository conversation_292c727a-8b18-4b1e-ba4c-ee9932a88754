<template>
  <view class="my-page">
    <!-- 顶部背景和头像/登录按钮区域 -->
<view class="header-bg">
    <!-- 背景图片 -->
    <image class="header-bg-image" :src="headerBgUrl" mode="aspectFill"></image>
    <view class="user-profile-box">
        <view class="avatar-container">
            <up-avatar 
                :size="54"  
                :src="isLoggedIn && userInfo && userInfo.avatarUrl ? userInfo.avatarUrl : defaultAvatarUrl"
                @click="!isLoggedIn ? goToLogin : null" 
            ></up-avatar>
            <!-- 覆盖在头像上的透明按钮：微信端触发头像选择器 -->
            <button
              v-if="isLoggedIn"
              class="avatar-choose-btn"
              open-type="chooseAvatar"
              @chooseavatar="onChooseAvatar"
            ></button>
        </view>
        <view class="user-text-box">
            <view v-if="isLoggedIn" class="user-info-container">
                <view class="username-row">
                    <text class="username-text">{{ getUserDisplayName }}</text>
                    <image class="edit-icon" :src="editIconUrl" mode="aspectFit" @click="handleEditNickname"></image>
                </view>
            </view>
            <view v-else class="login-button-container">
                <text class="username-text" @click="goToLogin">请登录</text>
            </view>
        </view>
    </view>

    <!-- 报名订单卡片 -->
    <view class="order-card-container" @click="handleOrderCardClick">
        <!-- 背景图片 -->
        <image class="order-card-bg-image" :src="orderCardBgUrl" mode="aspectFill"></image>
        <view class="order-card-content">
            <view class="order-card-left">
                <text class="order-card-text">报名订单</text>
            </view>
            <view class="order-card-right">
                <view class="order-card-action-wrapper">
                    <text class="order-card-action">查看</text>
                    <image class="order-card-arrow" :src="orderArrowUrl" mode="aspectFit"></image>
                </view>
            </view>
        </view>
    </view>
</view>

    <!-- 简化菜单区域 -->
    <view class="menu-list-card">
      <up-cell-group :border="false">
        <!-- 绑定手机号，根据登录状态显示值 -->
        <up-cell title="绑定手机号" :value="getPhoneDisplay" :isLink="false" :border="false">
          <template #icon>
            <image class="menu-icon" :src="phoneIconUrl" mode="aspectFit"></image>
          </template>
        </up-cell>
        <up-cell title="隐私政策" :isLink="true" arrow-direction="right" :border="false" @click="handleNavigate('/pages_sub/pages_other/policy?type=privacy_policy')">
          <template #icon>
            <image class="menu-icon" :src="privacyIconUrl" mode="aspectFit"></image>
          </template>
        </up-cell>
        <up-cell title="用户协议" :isLink="true" arrow-direction="right" :border="false" @click="handleNavigate('/pages_sub/pages_other/policy?type=user_agreement')">
          <template #icon>
            <image class="menu-icon" :src="contractIconUrl" mode="aspectFit"></image>
          </template>
        </up-cell>
        <up-cell title="注销账号" :isLink="true" arrow-direction="right" :border="false" @click="handleDeleteAccountClick">
          <template #icon>
            <image class="menu-icon" :src="deleteIconUrl" mode="aspectFit"></image>
          </template>
        </up-cell>
      </up-cell-group>
    </view>
	
	<view v-if="isLoggedIn" class="logout-button-wrapper">
	  <view class="custom-logout-btn" @click="logout">
	    退出登录
	  </view>
	</view>
    
    <!-- 自定义底部导航栏 -->
    <CustomTabBar :current="4" />
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'
import CustomTabBar from '@/components/layout/CustomTabBar.vue'
import { deleteAccountApi, updateUserInfoApi } from '@/api/data/user.js'
import { BASE_URL } from '@/utils/config.js'

const MAX_NICKNAME_LENGTH = 15

// 定义页面名称
defineOptions({
  name: 'ProfileIndex' // 页面名称与 pages.json 路径一致
})

// 响应式数据
const isLoggedIn = ref(false) // 初始登录状态
const userInfo = ref(null) // 用户信息
// 默认头像与其他图片URL （仅暗号映射，不再使用本地兜底）
const defaultAvatarUrl = ref('')
const headerBgUrl = ref('')
const editIconUrl = ref('') 
const orderArrowUrl = ref('') 
const phoneIconUrl = ref('') 
const contractIconUrl = ref('') 
const privacyIconUrl = ref('') 
const deleteIconUrl = ref('') 
const orderCardBgUrl = ref('') 

// 解析静态资源缓存：仅读取缓存暗号
const resolveAssetUrl = (assetKey) => {
  const assets = uni.getStorageSync('staticAssets')
  return (assets && assets[assetKey]) ? assets[assetKey] : ''
}

// 刷新默认头像（暗号：default-avatar）
const refreshDefaultAvatar = () => {
  defaultAvatarUrl.value = resolveAssetUrl('default-avatar')
}

// 刷新页面内其余静态资源（按暗号）
const refreshProfileAssets = () => {
  headerBgUrl.value = resolveAssetUrl('mybg')
  editIconUrl.value = resolveAssetUrl('my_edit')
  orderArrowUrl.value = resolveAssetUrl('group_right')
  phoneIconUrl.value = resolveAssetUrl('my_phone')
  contractIconUrl.value = resolveAssetUrl('my_contract')
  privacyIconUrl.value = resolveAssetUrl('my_personal')
  deleteIconUrl.value = resolveAssetUrl('my_delete')
  orderCardBgUrl.value = resolveAssetUrl('order-card-bg')
}

// 计算属性
const getUserDisplayName = computed(() => {
  if (!userInfo.value) {
    return '用户' // 默认显示
  }
  
  // 优先显示昵称，其次显示手机号，最后显示默认名称
  if (userInfo.value.nickname) {
    return userInfo.value.nickname
  }
  
  if (userInfo.value.phoneNumber) {
    // 如果有手机号，显示格式化的手机号
    const phone = userInfo.value.phoneNumber
    if (phone.length === 11) {
      return phone.substring(0, 3) + '****' + phone.substring(7)
    }
    return phone
  }
  
  return '用户' // 默认显示
})

const getPhoneDisplay = computed(() => {
  if (!isLoggedIn.value) {
    return '' // 未登录时不显示文案
  }
  
  // 检查手机号字段（可能是phone或phoneNumber）
  const phone = userInfo.value?.phone || userInfo.value?.phoneNumber
  
  if (!phone) {
    return '未绑定' // 已登录但没有手机号
  }
  
  // 格式化手机号显示（中间4位用*号替换）
  if (phone.length === 11) {
    return phone.substring(0, 3) + '****' + phone.substring(7)
  }
  
  // 如果手机号格式不标准，直接显示
  return phone
})

// 方法定义
// 统一的导航守卫方法
const handleNavigate = (url) => {
  // 检查登录状态
  if (!isLoggedIn.value) {
    // 未登录时，跳转到登录页
    console.log('用户未登录，跳转到登录页')
    goToLogin()
    return
  }
  
  // 已登录时，正常跳转到目标页面
  console.log('用户已登录，跳转到:', url)
  uni.navigateTo({
    url: url,
    fail: (err) => {
      console.error('页面跳转失败:', err)
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none',
        duration: 2000
      })
    }
  })
}

// 报名订单卡片点击处理
const handleOrderCardClick = () => {
  if (!isLoggedIn.value) {
    console.log('点击报名订单卡片，需要先登录')
    goToLogin()
  } else {
    console.log('点击报名订单卡片，跳转到订单页面')
    uni.navigateTo({
      url: '/pages_sub/pages_profile/orders',
      fail: (err) => {
        console.error('跳转订单页面失败:', err)
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none',
          duration: 2000
        })
      }
    })
  }
}


// 注销账号点击处理
const handleDeleteAccountClick = () => {
  if (!isLoggedIn.value) {
    console.log('注销账号需要先登录')
    goToLogin()
    return
  }
  
  // 已登录时，显示注销确认弹窗
  uni.showModal({
    title: '注销账号',
    content: '注销后账号将被标记为已注销，确定要继续吗？',
    confirmText: '确定注销',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        // 用户确认注销，调用注销方法
        console.log('用户确认注销账号')
        confirmDeleteAccount()
      }
    }
  })
}

// 确认注销账号的实际逻辑
const confirmDeleteAccount = async () => {
  console.log('=== 开始执行注销账号流程 ===')
  
  // 显示加载提示
  uni.showLoading({ title: '正在注销...' })
  
  try {
    console.log('调用后端注销接口...')
    const result = await deleteAccountApi()
    
    console.log('注销接口调用成功:', result)
    
    // 关闭加载提示
    uni.hideLoading()
    
    // 显示成功提示
    uni.showToast({
      title: '账号已成功注销',
      icon: 'success',
      duration: 2000
    })
    
    // 清空本地数据但不显示额外提示（注销成功提示已经显示过了）
    clearUserDataSilently()
    
    console.log('注销账号流程完成')
    
  } catch (error) {
    console.error('注销账号过程中发生错误:', error)
    
    // 关闭加载提示
    uni.hideLoading()
    
    // 显示错误提示
    uni.showToast({
      title: '注销失败，请稍后再试',
      icon: 'none',
      duration: 3000
    })
    
    // 显示详细错误信息（开发调试用）
    console.error('注销失败详情:', {
      message: error.message,
      stack: error.stack
    })
  }
}

// 检查登录状态的方法
const checkLoginStatus = () => {
  // 检查实际的token和用户信息
  const token = uni.getStorageSync('token')
  const userInfoData = uni.getStorageSync('userInfo')
  
  console.log('从本地存储获取的token:', token)
  console.log('从本地存储获取的userInfo:', userInfoData)
  
  const newLoginStatus = !!token
  console.log('计算出的登录状态:', newLoginStatus)
  console.log('当前页面登录状态:', isLoggedIn.value)
  
  // 强制更新登录状态和用户信息
  isLoggedIn.value = newLoginStatus
  userInfo.value = userInfoData || null
  
  if (isLoggedIn.value && userInfoData) {
    // 如果已登录且有用户信息，可以在这里设置用户数据
    console.log('用户已登录，用户信息:', userInfoData)
    if (userInfoData.phoneNumber) {
      console.log('用户手机号:', userInfoData.phoneNumber)
    }
  } else if (isLoggedIn.value && !userInfoData) {
    console.log('有token但无用户信息')
  } else {
    console.log('用户未登录')
  }
}

// 点击"请登录"按钮跳转到登录页面
const goToLogin = () => {
  uni.navigateTo({
    url: '/pages_sub/pages_other/login'
  })
}

// 点击"退出登录"按钮
const logout = (isDeleteAccount = false) => {
  // 如果是注销账号操作，直接清除数据，不显示确认弹窗
  if (isDeleteAccount) {
    clearUserData('已退出登录')
    return
  }
  
  // 正常退出登录时显示确认弹窗
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        clearUserData('已退出登录')
      }
    }
  })
}

// 编辑昵称点击处理
const handleEditNickname = () => {
  if (!isLoggedIn.value) {
    console.log('编辑昵称需要先登录')
    goToLogin()
    return
  }
  
  // 弹出输入框让用户输入新昵称
  uni.showModal({
    title: '修改昵称',
    editable: true,
    placeholderText: '请输入新昵称',
    content: userInfo.value?.nickname || '',
    success: async (res) => {
      if (res.confirm && res.content && res.content.trim()) {
        const newNickname = res.content.trim()
        
        // 检查昵称长度
        if (newNickname.length > MAX_NICKNAME_LENGTH) {
          uni.showToast({
            title: `昵称不能超过${MAX_NICKNAME_LENGTH}个字符`,
            icon: 'none',
            duration: 2000
          })
          return
        }
        
        // 调用API更新昵称
        await updateNickname(newNickname)
      }
    }
  })
}

// 更新昵称的方法
const updateNickname = async (newNickname) => {
  console.log('=== 开始更新昵称 ===')

  // 再次校验长度，双重保障
  if (!newNickname || newNickname.trim().length === 0) {
    uni.showToast({
      title: '昵称不能为空',
      icon: 'none',
      duration: 2000
    })
    return
  }
  if (newNickname.length > MAX_NICKNAME_LENGTH) {
    uni.showToast({
      title: `昵称不能超过${MAX_NICKNAME_LENGTH}个字符`,
      icon: 'none',
      duration: 2000
    })
    return
  }
  
  // 显示加载提示
  uni.showLoading({ title: '正在更新...' })
  
  try {
    console.log('调用updateUserInfoApi更新昵称:', newNickname)
    const result = await updateUserInfoApi({
      nickname: newNickname
    })
    
    console.log('昵称更新接口调用成功:', result)
    
    // 关闭加载提示
    uni.hideLoading()
    
    // 更新本地用户信息
    if (userInfo.value) {
      userInfo.value.nickname = newNickname
      // 同时更新本地存储
      uni.setStorageSync('userInfo', userInfo.value)
    }
    
    // 显示成功提示
    uni.showToast({
      title: '昵称修改成功',
      icon: 'success',
      duration: 2000
    })
    
    console.log('昵称更新完成')
    
  } catch (error) {
    console.error('更新昵称过程中发生错误:', error)
    
    // 关闭加载提示
    uni.hideLoading()
    
    // 显示错误提示
    uni.showToast({
      title: error.message || '更新失败，请稍后再试',
      icon: 'none',
      duration: 3000
    })
    
    console.error('昵称更新失败详情:', {
      message: error.message,
      stack: error.stack
    })
  }
}

// 清除用户数据的统一方法
const clearUserData = (message) => {
  console.log('=== 开始清除用户数据 ===')
  
  // 清除token和用户信息
  uni.removeStorageSync('token')
  uni.removeStorageSync('userInfo')
  
  // 更新页面状态
  isLoggedIn.value = false
  userInfo.value = null
  
  // 显示提示信息
  if (message) {
    uni.showToast({
      title: message,
      icon: 'success',
      duration: 1500
    })
  }
  
  console.log('用户数据已清除，UI已更新')
}

// 静默清除用户数据（不显示提示）
const clearUserDataSilently = () => {
  console.log('=== 静默清除用户数据 ===')
  
  // 清除token和用户信息
  uni.removeStorageSync('token')
  uni.removeStorageSync('userInfo')
  
  // 更新页面状态
  isLoggedIn.value = false
  userInfo.value = null
  
  console.log('用户数据已静默清除，UI已更新')
}

// 已移除模拟登录相关逻辑

// 生命周期钩子
onShow(() => {
  // 页面显示时隐藏原生 tabBar
  uni.hideTabBar();
  // 添加小延迟确保数据已保存
  setTimeout(() => {
    checkLoginStatus()
    refreshDefaultAvatar()
    refreshProfileAssets()
  }, 100)
})

onLoad(() => {
  checkLoginStatus()
  refreshDefaultAvatar()
  refreshProfileAssets()
})

// 选择头像回调（仅微信端可用）
const onChooseAvatar = (e) => {
  const tempPath = e?.detail?.avatarUrl
  if (!tempPath) return
  uploadAvatar(tempPath)
}

// 上传并保存头像
const uploadAvatar = async (filePath) => {
  if (!isLoggedIn.value) {
    goToLogin()
    return
  }
  const token = uni.getStorageSync('token')
  if (!token) {
    goToLogin()
    return
  }

  uni.showLoading({ title: '上传中...' })
  try {
    await new Promise((resolve, reject) => {
      uni.uploadFile({
        url: BASE_URL + '/common/upload',
        filePath,
        name: 'file',
        header: {
          Authorization: 'Bearer ' + token
        },
        success: async (res) => {
          try {
            const data = JSON.parse(res.data || '{}')
            if (res.statusCode === 200 && data.code === 200 && data.url) {
              const url = data.url
              await updateUserInfoApi({ avatarUrl: url })
              if (userInfo.value) {
                userInfo.value.avatarUrl = url
                uni.setStorageSync('userInfo', userInfo.value)
              }
              uni.showToast({ title: '头像已更新', icon: 'success' })
              resolve(true)
            } else {
              uni.showToast({ title: '上传失败', icon: 'none' })
              reject(new Error('upload error'))
            }
          } catch (err) {
            uni.showToast({ title: '响应解析失败', icon: 'none' })
            reject(err)
          }
        },
        fail: (err) => {
          uni.showToast({ title: '上传失败', icon: 'none' })
          reject(err)
        },
        complete: () => {
          uni.hideLoading()
        }
      })
    })
  } catch (e) {
    // 已提示
  }
}
</script>

<style lang="scss" scoped>
.my-page {
  background-color: #F5F5F5;
  min-height: 100vh;
}

/* --- 顶部区域 --- */
.header-bg {
    position: relative;
    height: 452rpx;
}

.header-bg-image {
    position: absolute;
    top:0;
    left: 0;
    width: 100%;
    height: 452rpx;
    z-index: 1;
}

.user-profile-box {
    position: absolute;
    /* 使用 calc() 动态计算正确的垂直位置 */
    top: calc(186rpx + var(--status-bar-height));
    /* 使用我们确认的左边距 */
    left: 32rpx;
    right: 40rpx; /* 增加一个右边距，防止编辑按钮贴边 */
    z-index: 2;
    display: flex;
    align-items: center;
}

.avatar-container {
  position: relative;
}

/* 覆盖在头像上的透明按钮，保持可点击 */
.avatar-choose-btn {
  position: absolute;
  top: 0;
  left: 0;
  width: 54px;
  height: 54px;
  background: transparent;
  border: none;
  padding: 0;
  opacity: 0;
}

.user-text-box {
    display: flex;
    flex-direction: column;
    margin-left: 20rpx;
    color: #ffffff;
}

.user-info-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.username-row {
    display: flex;
    align-items: center;
}

.username-text {
    max-width: 200rpx;
    height: 52rpx;
    font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;
    font-weight: normal;
    font-size: 36rpx;
    color: #FFFFFF;
    line-height: 52rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}





.edit-icon-box {
    display: none;
}

.edit-icon {
    width: 28rpx;
    height: 28rpx;
    margin-left: 16rpx;
}

/* --- 报名订单卡片 --- */
.order-card-container {
    position: absolute;
    top: calc(348rpx + var(--status-bar-height));
    left: 24rpx;
    right: 24rpx;
    height: 116rpx;
    box-sizing: border-box;
    z-index: 3;
    margin: 0;
    border-radius: 16rpx;
    overflow: hidden;
}

.order-card-bg-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    border-radius: 16rpx;
}

.order-card-content {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32rpx;
    height: 100%;
    z-index: 2;
}

.order-card-left {
	margin-left:68rpx;
    display: flex;
    align-items: center;
}

.order-card-text {
    color: #8C5E2D;
    font-size: 32rpx;
    font-weight: bold;
    margin-left: 16rpx;
}

.order-card-right {
    display: flex;
    align-items: center;
}

.order-card-action-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 116rpx;
    height: 40rpx;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    border: 1rpx solid #E69D3A;
    background-color: rgba(255, 255, 255, 0.1);
}

.order-card-action {
    // width: 48rpx;
    height: 36rpx;
    font-family: Alibaba PuHuiTi 3.0, Alibaba PuHuiTi 30;
    font-weight: normal;
    font-size: 24rpx;
    color: #452D03;
    line-height: 36rpx;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-right: 4rpx;
}

.order-card-arrow {
	width: 32rpx;
	height: 32rpx;
}

/* --- 白色列表 --- */
.menu-list-card {
    background-color: #ffffff;
    margin: 92rpx 24rpx 0 24rpx;
    border-radius: 20rpx;
    padding: 10rpx 0;
    position: relative;
    z-index: 2;
}

.menu-icon {
    width: 48rpx;
    height: 48rpx;
    margin-right: 20rpx;
}

.logout-button-wrapper {
  padding: 24rpx 0;
  margin: 0 24rpx; 
}

.custom-logout-btn {
  width: 702rpx;
  height: 76rpx;
  background: #FFFFFF;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #333333;
  
  &:active {
    background-color: #f5f5f5;
  }
}
</style>