<template>
  <view v-if="article" class="page-container">
    <view class="fixed-header" :style="{ height: headerHeight + 'px' }">
      <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
      <view class="custom-nav-bar"
            :style="{ height: navBarHeight + 'px', paddingBottom: navBarPaddingBottomPx + 'px' }">
        <view class="nav-back-button" @click="goBack">
          <u-icon name="arrow-left" color="#000000" size="22"></u-icon>
        </view>
        <view class="nav-title">资讯详情</view>
      </view>
    </view>

    <scroll-view scroll-y class="scrollable-content" :style="{ paddingTop: headerHeight + 'px' }">
      <view class="hero-section">
        <image class="hero-image" :src="getFullImageUrl(article.coverImageUrl)" mode="aspectFill"></image>
      </view>

      <view class="main-content">
        <view class="article-title">{{ article.title }}</view>

        <view class="article-meta-new">
          <view class="meta-left">
            <text class="meta-text">{{ article.source }}</text>
            <text class="meta-text">{{ formatDate(article.publishTime, 'YYYY-MM-DD') }}</text>
          </view>
          <view class="meta-right">
            <text class="meta-text">{{ article.viewCount }} 次阅读</text>
          </view>
        </view>

        <view class="summary-card" v-if="article.summary" :style="summaryCardStyle">
          <text class="summary-text">{{ article.summary }}</text>
        </view>

        <view class="content-card">
          <view class="content-body">
            <mp-html :content="article.content" :domain="imageBaseUrl" :tag-style="tagStyle" :preview-img="true"
                     lazy-load/>
          </view>
        </view>
      </view>

      <view class="tags-section-new" v-if="article.parsedTags && article.parsedTags.length > 0">
        <text class="tags-label">分类：</text>
        <text v-for="(tag, index) in article.parsedTags" :key="tag.id || tag.name" class="tag-item-new">
          {{ tag.name || tag }}{{ index < article.parsedTags.length - 1 ? ' / ' : '' }}
        </text>
      </view>

      <view class="comment-container">
        <view class="comment-header-section">
          <text class="comment-main-title">留言 ({{ commentTotal }})</text>
        </view>
        <view class="comment-input-card" @click="handleOpenCommentInput">
          <image
              class="comment-input-icon"
              :src="commentInputIconUrl"
          ></image>
          <text class="comment-input-placeholder">写留言</text>
        </view>

        <view v-if="commentList.length > 0" class="comment-list-container">
          <CommentItem
              v-for="comment in commentList"
              :key="comment.id"
              :comment="comment"
              @reply="handleReply"
          />
        </view>
        <view v-else class="empty-state">
          <view class="empty-icon">💬</view>
          <text class="empty-title">暂无评论</text>
          <text class="empty-desc">成为第一个发表看法的人吧</text>
        </view>
      </view>
    </scroll-view>

    <u-popup :show="replyState.showModal" @close="closeReplyModal" mode="bottom" round="20"
             :safe-area-inset-bottom="true">
    </u-popup>
    <ReplyPanel
        v-model:show="replyState.showModal"
        :reply-target="replyState.target"
        :is-submitting="replyState.isSubmitting"
        @submit="postReplyComment"
        @close="closeReplyModal"
    />
  </view>

  <view v-else-if="loadError" class="error-state">
    <view class="empty-icon">⚠️</view>
    <text class="empty-title">加载失败</text>
    <text class="empty-desc">无法获取文章内容，请稍后重试</text>
    <button class="retry-btn" @click="retryLoad">重新加载</button>
  </view>

  <u-popup :show="showNewCommentModal" @close="closeNewCommentModal" mode="bottom" round="20" :safe-area-inset-bottom="true">
    <view class="reply-popup">
      <view class="popup-header">
        <text class="popup-title">发表您的看法</text>
        <text class="popup-close" @click="closeNewCommentModal">×</text>
      </view>
      <view class="popup-body">
      <textarea
          class="reply-input"
          v-model="newComment.content"
          placeholder="分享你的想法..."
          :auto-height="true"
          maxlength="300"
      ></textarea>
        <view class="popup-footer">
          <text class="reply-counter">{{ newComment.content.length }}/300</text>
          <button
              class="reply-submit"
              :class="{'reply-submit-active': newComment.content.trim()}"
              :disabled="!newComment.content.trim() || newComment.isSubmitting"
              @click="postNewComment"
          >
            {{ newComment.isSubmitting ? '发布中...' : '发布' }}
          </button>
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script setup>
import { computed, nextTick, reactive, ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import mpHtml from '@/uni_modules/mp-html/components/mp-html/mp-html.vue';
import { getArticleDetail } from '@/api/content/article.js';
import { addComment, getCommentList } from '@/pages_sub/pages_article/api/content/comment.js';
import { tagStyle } from '@/pages_sub/pages_article/api/common/mpHtmlStyles.js';
import config from '@/utils/config.js';
import { getFullImageUrl } from '@/utils/image.js';
import { formatDate } from '@/utils/date.js';
import CommentItem from '@/components/common/CommentItem.vue';
import ReplyPanel from '@/components/common/ReplyPanel.vue';

const navBarPaddingBottomRpx = 20;
const navBarPaddingBottomPx = uni.upx2px(navBarPaddingBottomRpx);
const statusBarHeight = ref(0);
const navBarHeight = ref(0);
const headerHeight = ref(0);
const showNewCommentModal = ref(false);
const loadError = ref(false); // 新增加载错误状态
const assets = ref(uni.getStorageSync('staticAssets') || {});

const commentInputIconUrl = computed(() => {
  // 使用我们约定的“暗号” icon_article_comment_input
  return assets.value.icon_article_comment_input || '';
});

// 3. 为摘要卡片背景创建计算属性
const summaryCardStyle = computed(() => {
  // 使用我们约定的“暗号” bg_article_summary_card
  const imageUrl = assets.value.bg_article_summary_card;
  if (imageUrl) {
    // 成功获取到URL时，才生成样式对象
    return {
      backgroundImage: `url('${imageUrl}')`
    };
  }
  // 否则返回空对象
  return {};
});

const getNavBarInfo = () => {
  try {
    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
    statusBarHeight.value = menuButtonInfo.top;
    navBarHeight.value = menuButtonInfo.height;
    headerHeight.value = menuButtonInfo.bottom + navBarPaddingBottomPx;
  } catch (e) {
    const systemInfo = uni.getSystemInfoSync();
    statusBarHeight.value = systemInfo.statusBarHeight || 20;
    navBarHeight.value = 44;
    headerHeight.value = statusBarHeight.value + navBarHeight.value + navBarPaddingBottomPx;
  }
};

const getCurrentPageUrl = () => {
  try {
    const pages = getCurrentPages();
    const current = pages[pages.length - 1];
    const route = '/' + current.route;
    const options = current.options || {};
    const query = Object.keys(options)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(options[key])}`)
        .join('&');
    return query ? `${route}?${query}` : route;
  } catch (e) {
    return '/pages_sub/pages_article/detail';
  }
};

const ensureLoggedInForAction = () => {
  try {
    const token = uni.getStorageSync('token');
    if (!token) {
      const backUrl = getCurrentPageUrl();
      try { uni.setStorageSync('loginBackPage', backUrl); } catch (e) {}
      uni.navigateTo({ url: '/pages_sub/pages_other/login' });
      return false;
    }
    return true;
  } catch (e) {
    uni.navigateTo({ url: '/pages_sub/pages_other/login' });
    return false;
  }
};

const handleOpenCommentInput = () => {
  if (!ensureLoggedInForAction()) return;
  newComment.content = '';
  showNewCommentModal.value = true;
};

const closeNewCommentModal = () => {
  showNewCommentModal.value = false;
};

const goBack = () => {
  uni.navigateBack({ delta: 1 });
};

const article = ref(null);
const articleId = ref(null);
const commentList = ref([]);
const commentTotal = ref(0);
const newComment = reactive({ content: '', isSubmitting: false });
const replyState = reactive({
  showModal: false,
  target: null,
  isSubmitting: false
});
const isCommentsLoading = ref(false);

const imageBaseUrl = computed(() => {
  if (!config.imageBaseUrl) return '';
  return config.imageBaseUrl.replace(/\/$/, '');
});

const fetchArticleData = async (id) => {
  loadError.value = false;
  try {
    const response = await getArticleDetail(id);
    if (response.code === 200 && response.data) {
      const rawArticle = response.data;
      let parsedTags = [];
      if (rawArticle.tags && typeof rawArticle.tags === 'string') {
        try {
          parsedTags = JSON.parse(rawArticle.tags);
        } catch (e) {
          console.error('标签解析失败:', e);
        }
      } else if (Array.isArray(rawArticle.tags)) {
        parsedTags = rawArticle.tags;
      }
      if (!Array.isArray(parsedTags)) parsedTags = [];
      article.value = { ...rawArticle, parsedTags };
    } else {
      uni.showToast({ title: response.msg || '获取文章失败', icon: 'none' });
      loadError.value = true;
    }
  } catch (error) {
    console.error('获取文章失败:', error);
    uni.showToast({ title: '网络请求失败', icon: 'error' });
    loadError.value = true;
  }
};

const fetchComments = async () => {
  if (!articleId.value) return;
  isCommentsLoading.value = true;
  try {
    const response = await getCommentList({ relatedId: articleId.value, relatedType: 'article' });
    if (response.code === 200 && response.data) {
      commentList.value = response.data.comments || [];
      commentTotal.value = response.data.total || 0;
    } else {
      commentList.value = [];
      commentTotal.value = 0;
    }
  } catch (error) {
    commentList.value = [];
    commentTotal.value = 0;
  } finally {
    isCommentsLoading.value = false;
  }
};

const handlePostComment = async ({ content, parentId, stateObject }) => {
  if (!content.trim() || stateObject.isSubmitting) return;
  stateObject.isSubmitting = true;
  try {
    const response = await addComment({
      relatedId: articleId.value,
      relatedType: 'article',
      content: content.trim(),
      parentId: parentId || 0
    });
    if (response.code === 200) {
      uni.showToast({ title: '评论成功，待审核', icon: 'success' });
      await fetchComments();
      return true;
    } else {
      uni.showToast({ title: response.msg || '评论失败', icon: 'error' });
      return false;
    }
  } catch (error) {
    return false;
  } finally {
    stateObject.isSubmitting = false;
  }
};

const postNewComment = async () => {
  if (!ensureLoggedInForAction()) return;
  const success = await handlePostComment({ content: newComment.content, parentId: 0, stateObject: newComment });
  if (success) {
    newComment.content = '';
    closeNewCommentModal();
  }
};

const postReplyComment = async (content) => {
  if (!ensureLoggedInForAction()) return;
  const success = await handlePostComment({
    content: content,
    parentId: replyState.target.id,
    stateObject: replyState
  });
  if (success) {
    closeReplyModal();
  }
};

const handleReply = (comment) => {
  if (!ensureLoggedInForAction()) return;
  replyState.target = comment;
  replyState.showModal = true;
};

const closeReplyModal = () => {
  replyState.showModal = false;
  replyState.target = null;
};

const initPageData = async () => {
  if (!articleId.value) {
    loadError.value = true;
    return;
  }
  await fetchArticleData(articleId.value);
  // 只有在文章数据成功获取后才设置导航栏并获取评论
  if (article.value) {
    await nextTick();
    getNavBarInfo();
    await fetchComments();
  }
};

const retryLoad = () => {
  initPageData();
};

onLoad((options) => {
  articleId.value = options.id;
  initPageData();
});
</script>

<style lang="scss" scoped>
/* [核心修改] 移除了所有 .skeleton- 相关样式，其余样式保持不变 */
.page-container {
  height: 100vh;
  background-color: #FFFFFF;
}
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  background-color: #FFFFFF;
}
.scrollable-content {
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #FFFFFF;
  .empty-icon {
    font-size: 80rpx;
    margin-bottom: 24rpx;
    opacity: 0.5;
  }
  .empty-title {
    font-size: 30rpx;
    font-weight: 500;
    color: #606266;
    margin-bottom: 12rpx;
  }
  .empty-desc {
    font-size: 26rpx;
    color: #909399;
  }
  .retry-btn {
    margin-top: 40rpx;
    padding: 0 60rpx;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 28rpx;
    color: #ffffff;
    background-color: #3c9cff;
    border-radius: 35rpx;
  }
}
.status-bar {
  width: 100%;
}
.custom-nav-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-sizing: content-box;
}
.nav-back-button {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 15rpx;
}
.nav-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #000000;
}
.hero-section {
  position: relative;
  width: 100%;
  height: 500rpx;
  .hero-image {
    width: 100%;
    height: 100%;
  }
}
.main-content {
  padding: 0;
  margin-top: 0;
  position: relative;
  z-index: 3;
}
.article-title {
  font-size: 40rpx;
  color: #23232A;
  font-weight: 700;
  line-height: 1.5;
  padding: 30rpx 30rpx 0 30rpx;
  margin-bottom: 24rpx;
}
.article-meta-new {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  margin-bottom: 30rpx;
  .meta-left {
    display: flex;
    align-items: center;
    gap: 24rpx;
  }
  .meta-text {
    font-size: 24rpx;
    color: #9B9A9A;
  }
}
.summary-card {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin: 0 30rpx 0rpx 30rpx;
  padding: 30rpx;
  .summary-text {
    font-size: 30rpx;
    font-weight: 600;
    color: #495057;
    line-height: 1.8;
  }
}
.content-card {
  background: #FFFFFF;
  border-radius: 24rpx;
  margin: 0 30rpx 0 30rpx;
}
.tags-section-new {
  margin: 0 30rpx 0 30rpx;
  padding: 20rpx 24rpx;
  background: #F2F4FA;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8rpx;
  .tags-label {
    font-size: 24rpx;
    color: #66666E;
    font-weight: 600;
    margin-right: 12rpx;
  }
  .tag-item-new {
    font-size: 24rpx;
    color: #66666E;
  }
}
.comment-container {
  margin-top: 20rpx;
  padding: 0 30rpx 40rpx 30rpx;
  background: #ffffff;
}
.comment-header-section {
  padding: 40rpx 0 30rpx 0;
  border-bottom: 2rpx solid #f1f5f9;
  .comment-main-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #303133;
  }
}
.comment-input-card {
  width: 702rpx;
  height: 72rpx;
  background: #F7F7F7;
  border-radius: 8rpx;
  margin: 30rpx 0;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  box-sizing: border-box;
  .comment-input-icon {
    width: 32rpx;
    height: 32rpx;
    margin-right: 16rpx;
  }
  .comment-input-placeholder {
    font-size: 28rpx;
    color: #9B9A9A;
  }
}
.comment-list-container {
  padding-top: 20rpx;
}
.empty-state {
  padding: 80rpx 0;
  text-align: center;
  .empty-icon {
    font-size: 80rpx;
    margin-bottom: 24rpx;
    opacity: 0.5;
  }
  .empty-title {
    font-size: 30rpx;
    font-weight: 500;
    color: #606266;
    margin-bottom: 12rpx;
  }
  .empty-desc {
    font-size: 26rpx;
    color: #909399;
  }
}
.reply-popup {
  background: #ffffff;
  .popup-header {
    position: relative;
    padding: 30rpx;
    border-bottom: 2rpx solid #f1f5f9;
    text-align: center;
    .popup-title {
      font-size: 30rpx;
      font-weight: 500;
      color: #606266;
    }
    .popup-close {
      position: absolute;
      right: 30rpx;
      top: 50%;
      transform: translateY(-50%);
      font-size: 40rpx;
      color: #909399;
    }
  }
  .popup-body {
    padding: 30rpx;
    .reply-input {
      width: 100%;
      min-height: 200rpx;
      background: #f8f9fa;
      border: 2rpx solid #e5e7eb;
      border-radius: 16rpx;
      padding: 24rpx;
      font-size: 28rpx;
      line-height: 1.6;
      color: #303133;
    }
    .popup-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 20rpx;
      .reply-counter {
        font-size: 24rpx;
        color: #909399;
      }
      .reply-submit {
        padding: 16rpx 40rpx;
        border-radius: 30rpx;
        background: #dcdfe6;
        color: #ffffff;
        font-size: 28rpx;
        &.reply-submit-active {
          background: #3c9cff;
        }
      }
    }
  }
}
</style>