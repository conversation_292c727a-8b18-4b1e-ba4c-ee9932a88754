package com.hongda.wxapp.mapper;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;

/**
 * 小程序活动查询Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface WxEventMapper 
{
    /**
     * 获取不重复地区列表
     * 
     * @return 地区列表
     */
    public List<String> selectDistinctLocations();

    /**
     * 地区统计
     * 
     * @return 地区活动数量映射
     */
    public Map<String, Long> selectLocationStatistics();

    /**
     * 获取不重复城市列表
     * 
     * @return 城市列表
     */
    public List<String> selectDistinctCities();
} 