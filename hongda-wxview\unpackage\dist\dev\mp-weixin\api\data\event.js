"use strict";
const utils_request = require("../../utils/request.js");
const utils_config = require("../../utils/config.js");
const getEventListApi = (params) => {
  return utils_request.http.get(utils_config.API_PATHS.EVENT_LIST, params);
};
const getEventDetailApi = (id) => {
  return utils_request.http.get(`${utils_config.API_PATHS.EVENT_DETAIL}/${id}`);
};
const getHotEventListApi = (limit = 5) => {
  return utils_request.http.get(utils_config.API_PATHS.EVENT_LIST, {
    pageNum: 1,
    pageSize: limit,
    isHot: 1,
    status: 1
    // 只获取报名中的热门活动
  });
};
const getEventCitiesApi = () => {
  return utils_request.http.get("/events/cities");
};
const searchEventsApi = (params) => {
  return utils_request.http.get(utils_config.API_PATHS.EVENT_LIST, params);
};
const getCalendarEventsApi = (params) => {
  return utils_request.http.get("/events/calendar", params);
};
exports.getCalendarEventsApi = getCalendarEventsApi;
exports.getEventCitiesApi = getEventCitiesApi;
exports.getEventDetailApi = getEventDetailApi;
exports.getEventListApi = getEventListApi;
exports.getHotEventListApi = getHotEventListApi;
exports.searchEventsApi = searchEventsApi;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/api/data/event.js.map
