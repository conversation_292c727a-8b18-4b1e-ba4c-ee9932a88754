package com.hongda.wxapp.service.impl;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Comparator;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.hongda.content.domain.HongdaEvent;
import com.hongda.content.domain.HongdaFormDefinition;
import com.hongda.content.mapper.HongdaEventMapper;
import com.hongda.content.service.IHongdaFormDefinitionService;
import com.hongda.wxapp.mapper.WxEventMapper;
import com.hongda.wxapp.service.IWxEventService;

/**
 * 小程序活动Service实现类
 * 
 * <AUTHOR>
 */
@Service
public class WxEventServiceImpl implements IWxEventService 
{
    private static final Logger logger = LoggerFactory.getLogger(WxEventServiceImpl.class);
    
    @Autowired
    private HongdaEventMapper hongdaEventMapper;
    
    @Autowired
    private WxEventMapper wxEventMapper;
    
    @Autowired
    private IHongdaFormDefinitionService hongdaFormDefinitionService;
    
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 查询活动列表（小程序专用）
     */
    @Override
    public List<HongdaEvent> selectEventList(HongdaEvent hongdaEvent, String startTime, String endTime)
    {
        // 这里可以添加时间范围筛选逻辑
        // 暂时使用基础查询，后续可以扩展
        List<HongdaEvent> eventList = hongdaEventMapper.selectHongdaEventList(hongdaEvent);
        
        // 为每个活动计算实时状态
        if (eventList != null && !eventList.isEmpty()) {
            for (HongdaEvent event : eventList) {
                event.setStatus(event.calculateRealTimeStatus());
            }
        }
        
        return eventList;
    }

    /**
     * 查询活动详情
     */
    @Override
    public HongdaEvent selectEventById(Long id)
    {
        HongdaEvent event = hongdaEventMapper.selectHongdaEventById(id);
        if (event != null) {
            // 计算实时状态
            event.setStatus(event.calculateRealTimeStatus());
        }
        return event;
    }

    /**
     * 获取热门活动列表
     */
    @Override
    public List<HongdaEvent> selectHotEvents(Integer limit)
    {
        HongdaEvent queryEvent = new HongdaEvent();
        queryEvent.setIsHot(1);
        queryEvent.setStatus(1); // 只获取报名中的活动
        
        List<HongdaEvent> eventList = hongdaEventMapper.selectHongdaEventList(queryEvent);
        
        // 计算实时状态并限制数量
        if (eventList != null && !eventList.isEmpty()) {
            for (HongdaEvent event : eventList) {
                event.setStatus(event.calculateRealTimeStatus());
            }
            
            // 限制返回数量
            if (eventList.size() > limit) {
                eventList = eventList.subList(0, limit);
            }
        }
        
        return eventList;
    }

    /**
     * 获取即将开始的活动列表
     */
    @Override
    public List<HongdaEvent> selectUpcomingEvents(Integer limit)
    {
        HongdaEvent queryEvent = new HongdaEvent();
        queryEvent.setStatus(1); // 报名中的活动
        
        List<HongdaEvent> eventList = hongdaEventMapper.selectHongdaEventList(queryEvent);
        
        // 筛选即将开始的活动（接下来7天内）
        if (eventList != null && !eventList.isEmpty()) {
            Date now = new Date();
            Date nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
            
            eventList = eventList.stream()
                .filter(event -> {
                    Date startTime = event.getStartTime();
                    return startTime != null && startTime.after(now) && startTime.before(nextWeek);
                })
                .limit(limit)
                .toList();
            
            // 计算实时状态
            for (HongdaEvent event : eventList) {
                event.setStatus(event.calculateRealTimeStatus());
            }
        }
        
        return eventList;
    }

    /**
     * 获取所有不重复的活动地区
     */
    @Override
    public List<String> getDistinctLocations()
    {
        return wxEventMapper.selectDistinctLocations();
    }

    /**
     * 根据地区统计活动数量
     */
    @Override
    public Map<String, Long> getLocationStatistics()
    {
        return wxEventMapper.selectLocationStatistics();
    }

    /**
     * 搜索活动
     */
    @Override
    public List<HongdaEvent> searchEvents(String keyword)
    {
        HongdaEvent queryEvent = new HongdaEvent();
        queryEvent.setTitle(keyword);
        
        List<HongdaEvent> eventList = hongdaEventMapper.selectHongdaEventList(queryEvent);
        
        // 计算实时状态
        if (eventList != null && !eventList.isEmpty()) {
            for (HongdaEvent event : eventList) {
                event.setStatus(event.calculateRealTimeStatus());
            }
        }
        
        return eventList;
    }

    /**
     * 按地区获取活动列表
     */
    @Override
    public List<HongdaEvent> selectEventsByLocation(String location)
    {
        HongdaEvent queryEvent = new HongdaEvent();
        // 使用city字段进行城市筛选
        queryEvent.setCity(location);
        queryEvent.setStatus(1); // 只获取报名中的活动
        
        List<HongdaEvent> eventList = hongdaEventMapper.selectHongdaEventList(queryEvent);
        
        // 计算实时状态
        if (eventList != null && !eventList.isEmpty()) {
            for (HongdaEvent event : eventList) {
                event.setStatus(event.calculateRealTimeStatus());
            }
        }
        
        return eventList;
    }

    /**
     * 获取活动的表单定义
     */
    @Override
    public Object getEventFormDefinition(Long eventId)
    {
        try {
            // 1. 验证活动是否存在
            HongdaEvent event = hongdaEventMapper.selectHongdaEventById(eventId);
            if (event == null) {
                logger.error("活动不存在，eventId: {}", eventId);
                throw new RuntimeException("活动不存在");
            }
            logger.info("活动存在，eventId: {}, 活动标题: {}", eventId, event.getTitle());
            
            // 2. 查询数据库获取表单定义
            HongdaFormDefinition formDefinition = hongdaFormDefinitionService.selectFormDefinitionByEventId(eventId);
            
            if (formDefinition != null) {
                logger.info("表单定义详情，eventId: {}, id: {}, name: {}, fieldsJson是否为空: {}", 
                    eventId, formDefinition.getId(), formDefinition.getName(), 
                    formDefinition.getFieldsJson() == null || formDefinition.getFieldsJson().trim().isEmpty());
                if (formDefinition.getFieldsJson() != null) {
                    logger.info("fieldsJson内容: {}", formDefinition.getFieldsJson());
                }
            }
            
            if (formDefinition == null || formDefinition.getFieldsJson() == null || formDefinition.getFieldsJson().trim().isEmpty()) {
                // 如果没有自定义配置，返回默认配置
                logger.info("活动ID: {} 没有自定义表单配置，使用默认配置", eventId);
                return getDefaultFormDefinition();
            }
            
            // 3. 解析并转换表单定义格式
            String originalJson = formDefinition.getFieldsJson();
            logger.info("从数据库获取到的原始表单配置，eventId: {}, json: {}", eventId, originalJson);
            
            return convertFormCreateToSimpleFormat(originalJson);
            
        } catch (Exception e) {
            logger.error("获取表单定义失败，eventId: {}, 错误信息: {}", eventId, e.getMessage(), e);
            // 异常时返回默认配置，保证系统可用性
            return getDefaultFormDefinition();
        }
    }

    /**
     * 将form-create格式转换为前端期望的简化格式
     */
    private String convertFormCreateToSimpleFormat(String originalJson)
    {
        try {
            JsonNode formCreateData = objectMapper.readTree(originalJson);
            logger.info("开始解析原始JSON，数据类型: {}", formCreateData.getNodeType());
            
            // 创建结果对象
            ObjectNode result = objectMapper.createObjectNode();
            ArrayNode simpleFields = objectMapper.createArrayNode();
            
            JsonNode originalList = null;
            
            // 判断JSON的格式：可能是数组 [...] 或对象 {"list": [...]}
            if (formCreateData.isArray()) {
                // 直接是数组格式：[{...}, {...}]
                logger.info("检测到数组格式的JSON，直接使用");
                originalList = formCreateData;
            } else if (formCreateData.isObject() && formCreateData.has("list")) {
                // 对象格式且包含list字段：{"list": [{...}, {...}]}
                logger.info("检测到对象格式的JSON，使用list字段");
                originalList = formCreateData.get("list");
            } else {
                logger.warn("无法识别的JSON格式，既不是数组也不是包含list字段的对象");
                logger.warn("原始JSON内容: {}", originalJson);
                return getDefaultFormDefinition().toString();
            }
            
            if (originalList == null || !originalList.isArray()) {
                logger.warn("最终获取的字段列表不是数组格式");
                return getDefaultFormDefinition().toString();
            }
            
            logger.info("成功获取字段数组，字段数量: {}", originalList.size());
            
            // 转换每个字段
            for (JsonNode originalField : originalList) {
                ObjectNode simpleField = objectMapper.createObjectNode();
                
                // 基础字段映射
                if (originalField.has("type")) {
                    simpleField.put("type", originalField.get("type").asText());
                }
                if (originalField.has("field")) {
                    simpleField.put("field", originalField.get("field").asText());
                }
                if (originalField.has("title")) {
                    simpleField.put("label", originalField.get("title").asText()); // title -> label
                }
                
                // 必填字段转换: $required -> required
                if (originalField.has("$required")) {
                    simpleField.put("required", originalField.get("$required").asBoolean());
                } else if (originalField.has("required")) {
                    simpleField.put("required", originalField.get("required").asBoolean());
                } else {
                    simpleField.put("required", false);
                }
                
                // 保持props不变
                if (originalField.has("props")) {
                    simpleField.set("props", originalField.get("props"));
                }
                
                // 保持options不变（如果存在）
                if (originalField.has("options")) {
                    simpleField.set("options", originalField.get("options"));
                }
                
                simpleFields.add(simpleField);
                logger.debug("转换字段: {} -> {}", originalField.get("field"), simpleField.get("field"));
            }
            
            // 构造最终返回格式
            result.set("fields", simpleFields);
            
            String resultJson = objectMapper.writeValueAsString(result);
            logger.info("表单格式转换完成，转换后字段数量: {}", simpleFields.size());
            logger.debug("完整转换结果: {}", resultJson);
            
            return resultJson;
            
        } catch (Exception e) {
            logger.error("表单格式转换失败，原始JSON: {}, 错误信息: {}", originalJson, e.getMessage(), e);
            // 转换失败时返回默认配置
            return getDefaultFormDefinition().toString();
        }
    }

    /**
     * 获取默认的表单定义
     */
    private Object getDefaultFormDefinition()
    {
        // 返回一个默认的表单配置
        String defaultFormJson = """

        """;

        // 直接返回JSON字符串，让前端来解析
        // 这样保持与现有日志中看到的数据格式一致
        return defaultFormJson;
    }

    /**
     * 查询活动列表（支持三种排序方式和时间范围筛选）
     */
    @Override
    public List<HongdaEvent> selectEventListWithSort(HongdaEvent hongdaEvent, String startTime, String endTime, String timeRangeStart, String timeRangeEnd, String orderBy, String isAsc)
    {
        logger.info("开始查询活动列表，排序方式: orderBy={}, isAsc={}, 时间范围: {} ~ {}", orderBy, isAsc, timeRangeStart, timeRangeEnd);
        
        List<HongdaEvent> eventList;
        
        // 1. 判断排序方式
        if ("comprehensive".equals(orderBy)) {
            logger.info("使用综合排序算法");
            // 综合排序：先查询全部数据，然后Java内存排序
            eventList = hongdaEventMapper.selectHongdaEventList(hongdaEvent);
            logger.info("查询到 {} 条原始数据，开始综合排序计算", eventList != null ? eventList.size() : 0);
            eventList = selectEventListWithComprehensiveSort(eventList);
        } else if ("createTime".equals(orderBy)) {
            logger.info("使用最新发布排序: 创建时间越新排越前面，已结束/已取消活动排最后");
            // 最新发布：先按状态排序（正常状态在前），再按创建时间降序排序
            setOrderByClauseWithStatus("createTime", "desc");
            eventList = hongdaEventMapper.selectHongdaEventList(hongdaEvent);
            logger.info("最新发布排序完成，返回 {} 条数据", eventList != null ? eventList.size() : 0);
        } else if ("startTime".equals(orderBy)) {
            logger.info("使用最近开始排序: 开始时间越早排越前面，已结束/已取消活动排最后");
            // 最近开始：先按状态排序（正常状态在前），再按开始时间升序排序
            setOrderByClauseWithStatus("startTime", "asc");
            eventList = hongdaEventMapper.selectHongdaEventList(hongdaEvent);
            logger.info("最近开始排序完成，返回 {} 条数据", eventList != null ? eventList.size() : 0);
        } else {
            logger.info("使用默认排序: 综合排序");
            // 默认使用综合排序
            eventList = hongdaEventMapper.selectHongdaEventList(hongdaEvent);
            eventList = selectEventListWithComprehensiveSort(eventList);
        }
        
        // 2. 应用时间范围筛选（独立的筛选逻辑，不影响排序）
        if (eventList != null && !eventList.isEmpty()) {
            eventList = applyTimeRangeFilter(eventList, timeRangeStart, timeRangeEnd);
        }
        
        // 3. 为每个活动计算实时状态
        if (eventList != null && !eventList.isEmpty()) {
            for (HongdaEvent event : eventList) {
                event.setStatus(event.calculateRealTimeStatus());
            }
        }
        
        // 4. 不再按状态进行强制过滤（默认返回所有状态的活动）。
        //    如果前端传入 status，则 Mapper 层会按条件查询；未传入则返回全部。
        
        return eventList;
    }
    
    /**
     * 应用时间范围筛选 - 独立的时间筛选方法
     * @param eventList 活动列表
     * @param timeRangeStart 时间范围开始
     * @param timeRangeEnd 时间范围结束
     * @return 筛选后的活动列表
     */
    private List<HongdaEvent> applyTimeRangeFilter(List<HongdaEvent> eventList, String timeRangeStart, String timeRangeEnd) {
        // 如果没有时间范围限制，直接返回原列表
        if (timeRangeStart == null || timeRangeEnd == null) {
            logger.info("时间范围筛选: 无时间限制，返回全部活动");
            return eventList;
        }
        
        try {
            Date rangeStart = java.sql.Timestamp.valueOf(timeRangeStart.replace("T", " ").replace("Z", ""));
            Date rangeEnd = java.sql.Timestamp.valueOf(timeRangeEnd.replace("T", " ").replace("Z", ""));
            
            logger.info("时间范围筛选: {} ~ {}", rangeStart, rangeEnd);
            
            List<HongdaEvent> filteredList = eventList.stream()
                .filter(event -> {
                    Date eventStartTime = event.getStartTime();
                    if (eventStartTime == null) {
                        return false; // 没有开始时间的活动被过滤掉
                    }
                    
                    // 活动开始时间在指定范围内
                    return eventStartTime.compareTo(rangeStart) >= 0 && eventStartTime.compareTo(rangeEnd) <= 0;
                })
                .collect(java.util.stream.Collectors.toList());
            
            logger.info("时间范围筛选完成: 原有{}条活动，筛选后{}条活动", eventList.size(), filteredList.size());
            return filteredList;
            
        } catch (Exception e) {
            logger.error("时间范围筛选失败: {}", e.getMessage());
            return eventList; // 筛选失败时返回原列表
        }
    }
    
    /**
     * 设置MyBatis的排序子句
     */
    private void setOrderByClause(String orderBy, String isAsc) {
        if (orderBy == null || orderBy.trim().isEmpty()) {
            logger.warn("排序字段为空，跳过排序设置");
            return;
        }
        
        // 映射前端字段名到数据库字段名
        String dbColumn;
        switch (orderBy) {
            case "startTime":
                dbColumn = "start_time";
                break;
            case "createTime":
                dbColumn = "create_time";
                break;
            case "registeredCount":
                dbColumn = "registered_count";
                break;
            case "isHot":
                dbColumn = "is_hot";
                break;
            default:
                logger.warn("未知的排序字段: {}，使用默认排序", orderBy);
                dbColumn = "create_time"; // 默认排序
        }
        
        String direction = "asc".equalsIgnoreCase(isAsc) ? "ASC" : "DESC";
        String orderByClause = dbColumn + " " + direction;
        
        // 使用PageHelper设置排序
        try {
            Class<?> pageHelperClass = Class.forName("com.github.pagehelper.PageHelper");
            java.lang.reflect.Method method = pageHelperClass.getMethod("orderBy", String.class);
            method.invoke(null, orderByClause);
            logger.info("设置数据库排序成功: {}", orderByClause);
        } catch (Exception e) {
            logger.error("设置数据库排序失败: {}", e.getMessage());
            // 如果PageHelper不可用，记录日志但不中断流程
        }
    }

    /**
     * 设置带状态优先级的MyBatis排序子句
     * 已结束(status=2)和已取消(status=3)的活动排到最后
     */
    private void setOrderByClauseWithStatus(String orderBy, String isAsc) {
        if (orderBy == null || orderBy.trim().isEmpty()) {
            logger.warn("排序字段为空，跳过排序设置");
            return;
        }
        
        // 映射前端字段名到数据库字段名
        String dbColumn;
        switch (orderBy) {
            case "startTime":
                dbColumn = "start_time";
                break;
            case "createTime":
                dbColumn = "create_time";
                break;
            case "registeredCount":
                dbColumn = "registered_count";
                break;
            case "isHot":
                dbColumn = "is_hot";
                break;
            default:
                logger.warn("未知的排序字段: {}，使用默认排序", orderBy);
                dbColumn = "create_time"; // 默认排序
        }
        
        String direction = "asc".equalsIgnoreCase(isAsc) ? "ASC" : "DESC";
        
        // 复合排序：先按状态排序（正常状态在前），再按指定字段排序
        // 使用CASE WHEN让已结束(2)和已取消(3)状态排到最后
        String orderByClause = String.format(
            "CASE WHEN status IN (2, 3) THEN 1 ELSE 0 END ASC, %s %s", 
            dbColumn, direction
        );
        
        // 使用PageHelper设置排序
        try {
            Class<?> pageHelperClass = Class.forName("com.github.pagehelper.PageHelper");
            java.lang.reflect.Method method = pageHelperClass.getMethod("orderBy", String.class);
            method.invoke(null, orderByClause);
            logger.info("设置带状态优先级的数据库排序成功: {}", orderByClause);
        } catch (Exception e) {
            logger.error("设置带状态优先级的数据库排序失败: {}", e.getMessage());
            // 如果PageHelper不可用，记录日志但不中断流程
        }
    }

    /**
     * 综合排序算法实现
     */
    private List<HongdaEvent> selectEventListWithComprehensiveSort(List<HongdaEvent> events) {
        if (events == null || events.isEmpty()) {
            return events;
        }
        
        // 为每个活动计算综合得分
        for (HongdaEvent event : events) {
            double score = calculateComprehensiveScore(event);
            // 由于HongdaEvent没有comprehensiveScore字段，我们将得分存储在remark中（临时方案）
            event.setRemark(String.valueOf(score));
        }
        
        // 按综合得分排序（降序）
        events.sort((a, b) -> {
            double scoreA = Double.parseDouble(a.getRemark());
            double scoreB = Double.parseDouble(b.getRemark());
            return Double.compare(scoreB, scoreA);
        });
        
        // 清空remark字段
        for (HongdaEvent event : events) {
            event.setRemark(null);
        }
        
        return events;
    }

    /**
     * 计算综合得分
     * 综合得分 = 时效性得分 × 0.4 + 热门度得分 × 0.3 + 可报名性得分 × 0.2 + 新鲜度得分 × 0.1
     */
    private double calculateComprehensiveScore(HongdaEvent event) {
        double timeScore = calculateTimeScore(event);           // 时效性得分
        double popularityScore = calculatePopularityScore(event); // 热门度得分
        double availabilityScore = calculateAvailabilityScore(event); // 可报名性得分
        double freshnessScore = calculateFreshnessScore(event); // 新鲜度得分
        
        double totalScore = timeScore * 0.4 + popularityScore * 0.3 + 
                           availabilityScore * 0.2 + freshnessScore * 0.1;
        
        logger.debug("活动 {} 综合得分: 时效性={}, 热门度={}, 可报名性={}, 新鲜度={}, 总分={}", 
                    event.getTitle(), timeScore, popularityScore, availabilityScore, freshnessScore, totalScore);
        
        return totalScore;
    }

    /**
     * 时效性得分计算（40%权重）
     * 基于活动开始时间和状态
     */
    private double calculateTimeScore(HongdaEvent event) {
        LocalDateTime now = LocalDateTime.now();
        Date startTime = event.getStartTime();
        Integer status = event.getStatus();
        
        // 已结束或已取消
        if (status == 2 || status == 3) {
            return 0;
        }
        
        // 正在进行中
        if (status == 4) {
            return 70;
        }
        
        if (startTime == null) {
            return 40;
        }
        
        LocalDateTime startDateTime = startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        long hoursUntilStart = ChronoUnit.HOURS.between(now, startDateTime);
        
        if (hoursUntilStart <= 0) {
            return 40; // 已过期但状态未更新
        } else if (hoursUntilStart <= 24) {
            return 100; // 24小时内开始
        } else if (hoursUntilStart <= 168) { // 7天内
            return 90 - (hoursUntilStart - 24) / 24 * 2; // 线性递减
        } else {
            return Math.max(40, 70 - Math.log10(hoursUntilStart / 24) * 15);
        }
    }

    /**
     * 热门度得分计算（30%权重）
     * 基于is_hot标识和报名人数
     */
    private double calculatePopularityScore(HongdaEvent event) {
        double score = 0;
        
        // 热门活动基础分
        if (event.getIsHot() != null && event.getIsHot() == 1) {
            score += 40;
        }
        
        // 报名人数得分
        Long registeredCount = event.getRegisteredCount();
        if (registeredCount != null && registeredCount > 0) {
            score += Math.min(60, Math.log10(registeredCount + 1) * 20);
        }
        
        return Math.min(100, score);
    }

    /**
     * 可报名性得分计算（20%权重）
     * 基于活动状态和剩余名额
     */
    private double calculateAvailabilityScore(HongdaEvent event) {
        Integer status = event.getStatus();
        Long maxParticipants = event.getMaxParticipants();
        Long registeredCount = event.getRegisteredCount();
        
        // 已结束或已取消
        if (status == 2 || status == 3) {
            return 0;
        }
        
        // 未开始报名
        if (status == 0) {
            return 50;
        }
        
        // 报名中或进行中
        if (status == 1 || status == 4) {
            if (registeredCount == null) registeredCount = 0L;
            
            // 有人数限制的活动
            if (maxParticipants != null && maxParticipants > 0) {
                if (registeredCount >= maxParticipants) {
                    return 20; // 已满员
                }
                
                double remainingRatio = (double)(maxParticipants - registeredCount) / maxParticipants;
                
                if (remainingRatio > 0.5) {
                    return 100; // 余量充足
                } else if (remainingRatio > 0.2) {
                    return 80;  // 余量适中
                } else {
                    return 60;  // 余量紧张
                }
            } else {
                // 无人数限制或maxParticipants为0的活动
                return 90; // 可报名但非满分
            }
        }
        
        return 40;
    }

    /**
     * 新鲜度得分计算（10%权重）
     * 基于活动创建时间
     */
    private double calculateFreshnessScore(HongdaEvent event) {
        Date createTime = event.getCreateTime();
        if (createTime == null) {
            return 20;
        }
        
        LocalDateTime createDateTime = createTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime now = LocalDateTime.now();
        
        long hoursAge = ChronoUnit.HOURS.between(createDateTime, now);
        
        if (hoursAge <= 24) {
            return 100; // 24小时内发布
        } else if (hoursAge <= 168) { // 7天内
            return 90 - (hoursAge - 24) / 24 * 10; // 线性递减
        } else {
            return Math.max(20, 50 - Math.log10(hoursAge / 24) * 15);
        }
    }

    /**
     * 获取日历视图活动列表
     * 专门为日历视图优化：只返回状态0(未开始)和1(报名中)的活动，按开始时间升序排序（越早的越在前面）
     */
    @Override
    public List<HongdaEvent> selectCalendarEvents(HongdaEvent hongdaEvent, String timeRangeStart, String timeRangeEnd) {
        logger.info("开始查询日历视图活动列表，时间范围: {} ~ {}", timeRangeStart, timeRangeEnd);
        
        // 1. 查询所有活动（不使用PageHelper分页，因为需要先过滤再分页）
        List<HongdaEvent> eventList = hongdaEventMapper.selectHongdaEventList(hongdaEvent);
        logger.info("查询到原始活动数量: {}", eventList != null ? eventList.size() : 0);
        
        if (eventList == null || eventList.isEmpty()) {
            return eventList;
        }
        
        // 2. 筛选状态0(未开始)和1(报名中)的活动
        List<HongdaEvent> filteredByStatus = eventList.stream()
            .filter(event -> {
                // 计算实时状态
                Integer realTimeStatus = event.calculateRealTimeStatus();
                event.setStatus(realTimeStatus);
                
                // 只保留状态0和1的活动
                boolean isValidStatus = realTimeStatus != null && (realTimeStatus == 0 || realTimeStatus == 1);
                
                if (isValidStatus) {
                    logger.debug("保留活动: {} (状态: {})", event.getTitle(), realTimeStatus);
                } else {
                    logger.debug("过滤活动: {} (状态: {})", event.getTitle(), realTimeStatus);
                }
                
                return isValidStatus;
            })
            .collect(java.util.stream.Collectors.toList());
        
        logger.info("状态筛选后活动数量: {} (只保留状态0/1)", filteredByStatus.size());
        
        // 3. 应用时间范围筛选
        List<HongdaEvent> finalList = applyTimeRangeFilter(filteredByStatus, timeRangeStart, timeRangeEnd);
        
        // 4. 按开始时间排序（越早的越在前面）
        if (finalList != null && !finalList.isEmpty()) {
            Date now = new Date();
            finalList.sort((a, b) -> {
                Date startTimeA = a.getStartTime();
                Date startTimeB = b.getStartTime();
                
                // 处理null值
                if (startTimeA == null && startTimeB == null) return 0;
                if (startTimeA == null) return 1;
                if (startTimeB == null) return -1;
                
                // 按开始时间升序排序（越早的越在前面）
                return startTimeA.compareTo(startTimeB);
            });
        }
        
        logger.info("日历视图活动列表查询完成，最终返回: {} 条活动", finalList != null ? finalList.size() : 0);
        return finalList;
    }

    /**
     * 获取所有不重复的活动城市
     */
    @Override
    public List<String> getDistinctCities()
    {
        return wxEventMapper.selectDistinctCities();
    }

}