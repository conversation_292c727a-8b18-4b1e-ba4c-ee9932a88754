package com.hongda.wxapp.service.impl;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson2.JSONObject;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.core.domain.dto.UserProfileDTO;
import com.hongda.common.utils.DateUtils;
import com.hongda.common.utils.StringUtils;
import com.hongda.common.utils.http.HttpUtils;
import com.hongda.data.domain.HongdaUser;
import com.hongda.data.mapper.HongdaUserMapper;
import com.hongda.framework.web.service.TokenService;
import com.hongda.wxapp.service.IWxUserService;

/**
 * 微信小程序用户服务实现
 *
 * <AUTHOR>
 */
@Service
public class WxUserServiceImpl implements IWxUserService
{
    @Value("${wx.app-id}")
    private String appId;

    @Value("${wx.app-secret}")
    private String appSecret;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private HongdaUserMapper userMapper;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private HttpServletRequest request;

    /**
     * 微信小程序登录
     */
    @Override
    public AjaxResult wxLogin(String code)
    {
        System.out.println("=== 微信登录开始 ===");
        System.out.println("接收到的code: " + code);
        System.out.println("配置的appId: " + appId);
        System.out.println("配置的appSecret: " + (appSecret != null ? appSecret.substring(0, 8) + "..." : "null"));
        
        // 请求微信登录的地址
        String url = "https://api.weixin.qq.com/sns/jscode2session?appid={0}&secret={1}&js_code={2}&grant_type=authorization_code";
        String replaceUrl = url.replace("{0}", appId).replace("{1}", appSecret).replace("{2}", code);
        System.out.println("请求微信API的URL: " + replaceUrl.replace(appSecret, "***"));
        
        try
        {
            String response = HttpUtils.sendGet(replaceUrl);
            System.out.println("微信API响应: " + response);
            JSONObject jsonObject = JSONObject.parseObject(response);
            
            // 检查微信API是否返回错误
            Integer errcode = jsonObject.getInteger("errcode");
            if (errcode != null && errcode != 0) {
                String errmsg = jsonObject.getString("errmsg");
                System.out.println("微信API返回错误: errcode=" + errcode + ", errmsg=" + errmsg);
                return AjaxResult.error("微信登录失败: " + errmsg + " (错误代码: " + errcode + ")");
            }
            
            // 请求成功后获取数据
            String openid = jsonObject.getString("openid");
            String sessionKey = jsonObject.getString("session_key");
            String unionId = jsonObject.getString("unionid");
            
            System.out.println("微信API调用成功");
            System.out.println("获得openid: " + (openid != null ? openid.substring(0, 8) + "..." : "null"));
            System.out.println("获得session_key: " + (sessionKey != null ? "已获取" : "null"));
            
            // 存储openid到Redis
            redisTemplate.opsForValue().set("openid", openid);
            
            if (openid == null) {
                System.out.println("openid为空，登录失败");
                return AjaxResult.error("登录失败: 未能获取到用户标识");
            }
            
            // 查询用户是否存在（包含软删除的用户），用来判断是否为第一次登录或需要复活
            HongdaUser wxUser = userMapper.selectHongdaUserByOpenidIncludeDeleted(openid);
            
            HongdaUser user = new HongdaUser();
            if (wxUser == null) {
                // 不存在则创建新用户
                user.setOpenid(openid);
                user.setNickname(generateRandomUsername("用户"));
                user.setAvatarUrl(null); 
                if (unionId != null) {
                    user.setUnionid(unionId);
                }
                user.setStatus(0);
                user.setIsDeleted(0);  // 默认未删除
                user.setCreateTime(DateUtils.getNowDate());
                userMapper.insertHongdaUser(user);
                System.out.println("创建新用户，用户ID: " + user.getId());
            } else {
                user = wxUser;
                // 检查是否为软删除状态，如果是则复活
                if (Integer.valueOf(1).equals(user.getIsDeleted())) {
                    System.out.println("检测到软删除用户，执行复活操作，用户ID: " + user.getId());
                    userMapper.reactivateUserById(user.getId());
                    user.setIsDeleted(0);
                    user.setDeletedAt(null);
                    
                    // 重新生成随机昵称（用户复活时恢复正常状态）
                    if ("已注销用户".equals(user.getNickname())) {
                        user.setNickname(generateRandomUsername("用户"));
                        user.setAvatarUrl(null); 
                        System.out.println("为复活用户重新生成昵称: " + user.getNickname());
                    }
                }
                user.setUpdateTime(DateUtils.getNowDate());
                userMapper.updateHongdaUser(user);
                System.out.println("更新现有用户，用户ID: " + user.getId());
            }
            
            // 新增：禁用校验（后台禁用后，禁止登录与恢复）
            if (Integer.valueOf(1).equals(user.getStatus())) {
                return AjaxResult.error(403, "账号已被禁用，请联系管理员");
            }

            // 创建Token
            String token = tokenService.createTokenForWxUser(user.getId());
            System.out.println("Token创建成功，用户ID: " + user.getId());
            
            AjaxResult ajax = AjaxResult.success();
            ajax.put("token", token);
            ajax.put("userInfo", user);
            System.out.println("=== 微信登录成功完成 ===");
            return ajax;
        }
        catch (Exception e)
        {
            System.out.println("微信登录异常: " + e.getMessage());
            e.printStackTrace();
            return AjaxResult.error("登录失败：" + e.getMessage());
        }
    }

    /**
     * 获取手机号
     */
    @Override
    public AjaxResult getPhoneNumber(String code)
    {
        System.out.println("=== 获取手机号开始 ===");
        System.out.println("接收到的phoneCode: " + code);

        try
        {
            // 通过当前登录用户ID获取openid
            Long userId = tokenService.getWxUserId(request);
            System.out.println("当前登录用户ID: " + userId);
            if (userId == null) {
                System.out.println("用户未登录");
                return AjaxResult.error("用户未登录");
            }

            HongdaUser user = userMapper.selectHongdaUserById(userId);
            if (user == null) {
                System.out.println("用户不存在，用户ID: " + userId);
                return AjaxResult.error("用户不存在");
            }
            System.out.println("找到用户，openid: " + (user.getOpenid() != null ? user.getOpenid().substring(0, 8) + "..." : "null"));

            // 增加一次自动刷新重试，避免 access_token 过期导致 40001 报错
            int maxAttempt = 2;
            for (int attempt = 1; attempt <= maxAttempt; attempt++) {
                AjaxResult accessTokenResult = getAccessToken();
                String accessToken = (String) accessTokenResult.get("access_token");
                System.out.println("[尝试 " + attempt + "/" + maxAttempt + "] 获得AccessToken: " + (accessToken != null ? accessToken.substring(0, 8) + "..." : "null"));

                if (accessToken == null) {
                    // getAccessToken 失败
                    String msg = (String) accessTokenResult.get("msg");
                    return AjaxResult.error(StringUtils.isNotBlank(msg) ? msg : "获取AccessToken失败");
                }

                String phoneUrl = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=" + accessToken;
                System.out.println("请求手机号API: " + phoneUrl.replace(accessToken, "***"));

                Map<String, Object> map = new LinkedHashMap<>();
                map.put("code", code);
                System.out.println("请求参数: " + JSONObject.toJSONString(map));

                String res = HttpUtils.sendPost(phoneUrl, JSONObject.toJSONString(map));
                System.out.println("微信手机号API响应: " + res);
                JSONObject jsonObject = JSONObject.parseObject(res);

                Integer errcode = jsonObject.getInteger("errcode");
                System.out.println("微信手机号API返回的errcode: " + errcode);

                if (errcode != null && errcode == 0) {
                    JSONObject phoneInfo = jsonObject.getJSONObject("phone_info");
                    String phoneNumber = phoneInfo.getString("phoneNumber");
                    System.out.println("成功获得手机号: " + (phoneNumber != null ? phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(7) : "null"));

                    // 生成手机号哈希值
                    String phoneHash = generatePhoneHash(phoneNumber);
                    System.out.println("生成手机号哈希: " + (phoneHash != null ? phoneHash.substring(0, 8) + "..." : "null"));

                    // 检查是否有使用该手机号的软删除账号需要复活
                    HongdaUser deletedUser = userMapper.selectDeletedByPhoneHash(phoneHash);
                    if (deletedUser != null && !deletedUser.getId().equals(user.getId())) {
                        System.out.println("检测到同手机号的软删除账号，执行复活并切换会话，原账号ID: " + deletedUser.getId());
                        
                        // 复活旧账号
                        userMapper.reactivateUserById(deletedUser.getId());
                        
                        // 更新旧账号信息，绑定当前微信
                        HongdaUser updateUser = new HongdaUser();
                        updateUser.setId(deletedUser.getId());
                        updateUser.setPhone(phoneNumber);
                        updateUser.setPhoneHash(phoneHash);
                        updateUser.setOpenid(user.getOpenid());  // 将当前微信绑定到旧账号
                        if (user.getUnionid() != null) {
                            updateUser.setUnionid(user.getUnionid());
                        }
                        
                        // 如果是已注销用户，重新生成随机昵称
                        if ("已注销用户".equals(deletedUser.getNickname())) {
                            updateUser.setNickname(generateRandomUsername("用户"));
                            updateUser.setAvatarUrl(null);  // 手机号复活时清除默认头像，让前端处理
                            System.out.println("为手机号复活用户重新生成昵称: " + updateUser.getNickname());
                        }
                        
                        updateUser.setUpdateTime(new Date());
                        userMapper.updateHongdaUser(updateUser);
                        
                        // 读取复活后的账号信息
                        HongdaUser reactivatedUser = userMapper.selectHongdaUserById(deletedUser.getId());

                        // 禁用账号不允许恢复使用，直接提示并不发放token
                        if (Integer.valueOf(1).equals(reactivatedUser.getStatus())) {
                            System.out.println("复活账号处于禁用状态，禁止发放token，账号ID: " + deletedUser.getId());
                            return AjaxResult.error(403, "账号已被禁用，请联系管理员");
                        }

                        // 为复活的账号创建新token
                        String newToken = tokenService.createTokenForWxUser(deletedUser.getId());

                        // 返回复活账号的信息和新token
                        AjaxResult ajax = AjaxResult.success();
                        ajax.put("phoneNumber", phoneNumber);
                        ajax.put("token", newToken);
                        ajax.put("userInfo", reactivatedUser);
                        ajax.put("message", "检测到您之前的账号，已自动恢复账号和历史数据");
                        System.out.println("账号复活完成，切换到账号ID: " + deletedUser.getId());
                        return ajax;
                    }

                    // 否则给当前账号正常绑定手机号
                    user.setPhone(phoneNumber);
                    user.setPhoneHash(phoneHash);
                    userMapper.updateHongdaUser(user);
                    System.out.println("手机号已更新到数据库");

                    AjaxResult ajax = AjaxResult.success();
                    ajax.put("phoneNumber", phoneNumber);
                    System.out.println("=== 获取手机号成功完成 ===");

                    return ajax;
                } else {
                    String errmsg = jsonObject.getString("errmsg");
                    System.out.println("微信手机号API返回错误: errcode=" + errcode + ", errmsg=" + errmsg);

                    // access_token 失效类错误：清除缓存并重试一次
                    if (errcode != null && (errcode == 40001 || errcode == 42001 || errcode == 40014)) {
                        System.out.println("检测到access_token失效，清理缓存并准备重试...");
                        try {
                            redisTemplate.delete("accessToken");
                        } catch (Exception ignore) {}

                        if (attempt < maxAttempt) {
                            continue; // 进行下一次尝试
                        }
                    }

                    // 非可重试错误或已重试仍失败
                    return AjaxResult.error("微信错误：" + errmsg + " (错误代码: " + errcode + ")");
                }
            }

            // 理论上不会走到这里
            return AjaxResult.error("获取手机号失败：未知错误");
        }
        catch (Exception e)
        {
            System.out.println("获取手机号异常: " + e.getMessage());
            e.printStackTrace();
            return AjaxResult.error("获取手机号失败：" + e.getMessage());
        }
    }

    /**
     * getAccessToken方法 经常会使用则被封装了
     */
    @Override
    public AjaxResult getAccessToken()
    {
        String accToken = redisTemplate.opsForValue().get("accessToken");
        if (StringUtils.isBlank(accToken)) {
            String url = "https://api.weixin.qq.com/cgi-bin/token?appid={0}&secret={1}&grant_type=client_credential";
            String replaceUrl = url.replace("{0}", appId).replace("{1}", appSecret);
            
            try
            {
                String response = HttpUtils.sendGet(replaceUrl);
                JSONObject jsonObject = JSONObject.parseObject(response);
                
                Integer errcode = jsonObject.getInteger("errcode");
                if (errcode == null) {
                    String accessToken = jsonObject.getString("access_token");
                    redisTemplate.opsForValue().set("accessToken", accessToken, 7000, TimeUnit.SECONDS);
                    
                    AjaxResult ajax = AjaxResult.success();
                    ajax.put("access_token", accessToken);
                    return ajax;
                } else {
                    return AjaxResult.error("微信错误：" + jsonObject.getString("errmsg"));
                }
            }
            catch (Exception e)
            {
                return AjaxResult.error("获取AccessToken失败：" + e.getMessage());
            }
        } else {
            AjaxResult ajax = AjaxResult.success();
            ajax.put("access_token", accToken);
            return ajax;
        }
    }

    /**
     * 获取用户信息
     */
    @Override
    public AjaxResult getUserInfo(Long userId)
    {
        try
        {
            HongdaUser user = userMapper.selectHongdaUserById(userId);
            if (user == null)
            {
                return AjaxResult.error("用户不存在");
            }
            
            // 检查用户是否被软删除
            if (Integer.valueOf(1).equals(user.getIsDeleted())) {
                return AjaxResult.error("账号已注销，请重新登录");
            }
            
            // 新增：禁用校验
            if (Integer.valueOf(1).equals(user.getStatus())) {
                return AjaxResult.error(403, "账号已被禁用，请联系管理员");
            }
            
            // 返回用户信息（不包含敏感信息）
            AjaxResult ajax = AjaxResult.success();
            ajax.put("userInfo", user);
            return ajax;
        }
        catch (Exception e)
        {
            return AjaxResult.error("获取用户信息失败：" + e.getMessage());
        }
    }

    /**
     * 更新用户资料
     */
    @Override
    public AjaxResult updateProfile(Long userId, UserProfileDTO profileDTO)
    {
        try
        {
            HongdaUser userUpdate = new HongdaUser();
            userUpdate.setId(userId);

            if (StringUtils.isNotBlank(profileDTO.getNickname()))
            {
                userUpdate.setNickname(profileDTO.getNickname());
            }
            if (StringUtils.isNotBlank(profileDTO.getAvatarUrl()))
            {
                userUpdate.setAvatarUrl(profileDTO.getAvatarUrl());
            }
            
            userUpdate.setUpdateTime(new Date());
            int result = userMapper.updateHongdaUser(userUpdate);
            
            if (result > 0)
            {
                return AjaxResult.success("更新成功");
            }
            else
            {
                return AjaxResult.error("更新失败：用户不存在");
            }
        }
        catch (Exception e)
        {
            return AjaxResult.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除用户账号（软删除 + 匿名化）
     */
    @Override
    public AjaxResult deleteAccount(Long userId)
    {
        System.out.println("=== 开始软删除用户账号 ===");
        System.out.println("用户ID: " + userId);
        
        try
        {
            // 验证用户是否存在且未被软删除
            HongdaUser user = userMapper.selectHongdaUserById(userId);
            if (user == null)
            {
                return AjaxResult.error("用户不存在");
            }
            
            if (Integer.valueOf(1).equals(user.getIsDeleted())) {
                return AjaxResult.error("账号已经注销");
            }

            // 获取原始手机号用于生成哈希
            String phone = user.getPhone();
            String phoneHash = generatePhoneHash(phone);
            System.out.println("生成手机号哈希用于复活: " + (phoneHash != null ? phoneHash.substring(0, 8) + "..." : "null"));

            // 执行软删除和匿名化
            HongdaUser updateUser = new HongdaUser();
            updateUser.setId(userId);
            updateUser.setIsDeleted(1);               // 标记为已删除
            updateUser.setDeletedAt(new Date());      // 记录删除时间
            updateUser.setNickname("已注销用户");      // 匿名化昵称
            updateUser.setAvatarUrl(null);            // 清除头像
            updateUser.setPhone(null);                // 释放手机号唯一约束
            updateUser.setPhoneHash(phoneHash);       // 保存哈希用于复活
            updateUser.setUpdateTime(new Date());
            
            int result = userMapper.updateHongdaUser(updateUser);
            if (result > 0)
            {
                System.out.println("软删除成功，用户数据已匿名化");
                
                // 吊销用户会话（可选，根据TokenService实现）
                try {
                    // tokenService.invalidateUser(userId); // 如果有此方法的话
                    System.out.println("用户会话已吊销");
                } catch (Exception ignore) {
                    System.out.println("吊销会话时出现异常，但不影响注销流程");
                }
                
                // TODO: 可以添加异步清理逻辑，比如：
                // 1. 发布用户删除事件，清理相关业务数据
                // 2. 清理Redis中的用户缓存
                // 3. 记录注销日志
                // userCleanupPublisher.publishUserDeleted(userId);
                
                System.out.println("=== 用户账号注销完成 ===");
                return AjaxResult.success("账号已成功注销，您可以随时使用相同微信或手机号登录来恢复账号");
            }
            else
            {
                return AjaxResult.error("注销失败，请稍后再试");
            }
        }
        catch (Exception e)
        {
            System.out.println("注销账号时发生异常: " + e.getMessage());
            e.printStackTrace();
            return AjaxResult.error("注销失败：" + e.getMessage());
        }
    }

    /**
     * 生成随机用户名
     */
    private String generateRandomUsername(String prefix)
    {
        return prefix + System.currentTimeMillis();
    }

    /**
     * 生成手机号哈希值
     */
    @Override
    public String generatePhoneHash(String phone)
    {
        if (phone == null || phone.trim().isEmpty()) {
            return null;
        }
        
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(phone.getBytes("UTF-8"));
            
            // 转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            System.out.println("生成手机号哈希值失败: " + e.getMessage());
            return null;
        }
    }

}