package com.hongda.wxapp.controller;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.hongda.common.core.controller.BaseController;
import com.hongda.common.core.domain.AjaxResult;
import com.hongda.common.core.page.TableDataInfo;
import com.hongda.content.domain.HongdaEvent;
import com.hongda.wxapp.service.IWxEventService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 小程序活动接口控制器
 * 
 * <AUTHOR>
 */
@Tag(name = "小程序活动接口", description = "小程序端活动相关接口")
@RestController
@RequestMapping("/api/v1/events")
public class WxEventController extends BaseController
{
    private static final Logger logger = LoggerFactory.getLogger(WxEventController.class);
    
    @Autowired
    private IWxEventService wxEventService;

    /**
     * 获取活动列表（小程序专用）
     */
    @Operation(summary = "获取活动列表", description = "分页获取活动列表，支持搜索和筛选")
    @GetMapping("/list")
    public TableDataInfo getEventList(
        @Parameter(description = "页码", required = false) @RequestParam(defaultValue = "1") Integer pageNum,
        @Parameter(description = "每页数量", required = false) @RequestParam(defaultValue = "10") Integer pageSize,
        @Parameter(description = "搜索关键词", required = false) @RequestParam(required = false) String title,
        @Parameter(description = "活动地点", required = false) @RequestParam(required = false) String location,
        @Parameter(description = "活动状态", required = false) @RequestParam(required = false) Integer status,
        @Parameter(description = "开始时间", required = false) @RequestParam(required = false) String startTime,
        @Parameter(description = "结束时间", required = false) @RequestParam(required = false) String endTime,
        @Parameter(description = "时间范围开始", required = false) @RequestParam(required = false) String timeRangeStart,
        @Parameter(description = "时间范围结束", required = false) @RequestParam(required = false) String timeRangeEnd,
        @Parameter(description = "排序字段", required = false) @RequestParam(required = false) String orderBy,
        @Parameter(description = "是否升序", required = false) @RequestParam(required = false) String isAsc,
        @Parameter(description = "是否热门", required = false) @RequestParam(required = false) Integer isHot
    )
    {
        logger.info("Controller接收参数: pageNum={}, pageSize={}, title={}, location={}, status={}, startTime={}, endTime={}, timeRangeStart={}, timeRangeEnd={}, orderBy={}, isAsc={}, isHot={}",
                    pageNum, pageSize, title, location, status, startTime, endTime, timeRangeStart, timeRangeEnd, orderBy, isAsc, isHot);
        
        if (location != null && !location.trim().isEmpty()) {
            logger.info("城市筛选: 前端传递城市名='{}', 将匹配数据库中的'{}'或'{}'", location, location, location + "市");
        }
        
        // 构建查询对象
        HongdaEvent queryEvent = new HongdaEvent();
        queryEvent.setTitle(title);
        // 地区筛选：使用city字段而不是location字段
        queryEvent.setCity(location);
        queryEvent.setStatus(status);
        queryEvent.setIsHot(isHot);
        
        startPage();
        List<HongdaEvent> list = wxEventService.selectEventListWithSort(queryEvent, startTime, endTime, timeRangeStart, timeRangeEnd, orderBy, isAsc);
        return getDataTable(list);
    }

    /**
     * 获取活动详情
     */
    @Operation(summary = "获取活动详情", description = "根据活动ID获取活动详细信息")
    @GetMapping("/{id}")
    public AjaxResult getEventDetail(@Parameter(description = "活动ID", required = true) @PathVariable("id") Long id)
    {
        HongdaEvent event = wxEventService.selectEventById(id);
        if (event == null)
        {
            return AjaxResult.error("活动不存在");
        }
        return AjaxResult.success(event);
    }

    /**
     * 获取热门活动列表
     */
    @Operation(summary = "获取热门活动", description = "获取热门活动列表，用于首页展示")
    @GetMapping("/hot")
    public AjaxResult getHotEvents(@Parameter(description = "限制数量", required = false) @RequestParam(defaultValue = "5") Integer limit)
    {
        List<HongdaEvent> hotEvents = wxEventService.selectHotEvents(limit);
        return AjaxResult.success(hotEvents);
    }

    /**
     * 获取即将开始的活动
     */
    @Operation(summary = "获取即将开始的活动", description = "获取即将开始的活动列表")
    @GetMapping("/upcoming")
    public AjaxResult getUpcomingEvents(@Parameter(description = "限制数量", required = false) @RequestParam(defaultValue = "10") Integer limit)
    {
        List<HongdaEvent> upcomingEvents = wxEventService.selectUpcomingEvents(limit);
        return AjaxResult.success(upcomingEvents);
    }

    /**
     * 获取活动地区列表
     */
    @Operation(summary = "获取活动地区列表", description = "获取所有活动的不重复地区列表，用于筛选")
    @GetMapping("/locations")
    public AjaxResult getEventLocations()
    {
        List<String> locations = wxEventService.getDistinctLocations();
        return AjaxResult.success(locations);
    }

    /**
     * 获取地区统计信息
     */
    @Operation(summary = "获取地区统计", description = "获取各地区活动数量统计")
    @GetMapping("/location-stats")
    public AjaxResult getLocationStatistics()
    {
        Map<String, Long> stats = wxEventService.getLocationStatistics();
        return AjaxResult.success(stats);
    }

    /**
     * 搜索活动
     */
    @Operation(summary = "搜索活动", description = "根据关键词搜索活动")
    @GetMapping("/search")
    public TableDataInfo searchEvents(
        @Parameter(description = "搜索关键词", required = true) @RequestParam String keyword,
        @Parameter(description = "页码", required = false) @RequestParam(defaultValue = "1") Integer pageNum,
        @Parameter(description = "每页数量", required = false) @RequestParam(defaultValue = "10") Integer pageSize
    )
    {
        if (keyword == null || keyword.trim().isEmpty())
        {
            return getDataTable(List.of());
        }
        
        startPage();
        List<HongdaEvent> list = wxEventService.searchEvents(keyword.trim());
        return getDataTable(list);
    }

    /**
     * 按地区获取活动
     */
    @Operation(summary = "按地区获取活动", description = "获取指定地区的活动列表")
    @GetMapping("/by-location")
    public TableDataInfo getEventsByLocation(
        @Parameter(description = "地区名称", required = true) @RequestParam String location,
        @Parameter(description = "页码", required = false) @RequestParam(defaultValue = "1") Integer pageNum,
        @Parameter(description = "每页数量", required = false) @RequestParam(defaultValue = "10") Integer pageSize
    )
    {
        startPage();
        List<HongdaEvent> list = wxEventService.selectEventsByLocation(location);
        return getDataTable(list);
    }

    /**
     * 获取日历视图活动列表
     */
    @Operation(summary = "获取日历视图活动", description = "专门为日历视图优化的活动列表，只返回状态0/1的活动，按开始时间升序排序")
    @GetMapping("/calendar")
    public TableDataInfo getCalendarEvents(
        @Parameter(description = "页码", required = false) @RequestParam(defaultValue = "1") Integer pageNum,
        @Parameter(description = "每页数量", required = false) @RequestParam(defaultValue = "10") Integer pageSize,
        @Parameter(description = "搜索关键词", required = false) @RequestParam(required = false) String title,
        @Parameter(description = "活动地点", required = false) @RequestParam(required = false) String location,
        @Parameter(description = "时间范围开始", required = false) @RequestParam(required = false) String timeRangeStart,
        @Parameter(description = "时间范围结束", required = false) @RequestParam(required = false) String timeRangeEnd
    )
    {
        logger.info("🗓️ 日历视图接口: pageNum={}, pageSize={}, title={}, location={}, timeRangeStart={}, timeRangeEnd={}", 
                    pageNum, pageSize, title, location, timeRangeStart, timeRangeEnd);
        
        // 构建查询对象，只查询状态0(未开始)和1(报名中)的活动
        HongdaEvent queryEvent = new HongdaEvent();
        queryEvent.setTitle(title);
        queryEvent.setCity(location);
        // 不设置status，在Service层处理状态筛选
        
        // 日历视图需要先获取所有数据进行过滤，然后手动分页
        List<HongdaEvent> allEvents = wxEventService.selectCalendarEvents(queryEvent, timeRangeStart, timeRangeEnd);
        
        // 手动分页
        int total = allEvents.size();
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, total);
        
        List<HongdaEvent> pagedEvents;
        if (startIndex >= total) {
            pagedEvents = new java.util.ArrayList<>();
        } else {
            pagedEvents = allEvents.subList(startIndex, endIndex);
        }
        
        // 构建分页结果
        TableDataInfo dataTable = new TableDataInfo();
        dataTable.setRows(pagedEvents);
        dataTable.setTotal(total);
        dataTable.setCode(200);
        dataTable.setMsg("查询成功");
        
        return dataTable;
    }

    /**
     * 获取活动城市列表
     */
    @Operation(summary = "获取活动城市列表", description = "获取所有活动的不重复城市列表，用于地区筛选")
    @GetMapping("/cities")
    public AjaxResult getEventCities()
    {
        List<String> cities = wxEventService.getDistinctCities();
        return AjaxResult.success(cities);
    }


} 