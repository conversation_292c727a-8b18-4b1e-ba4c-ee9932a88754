{"version": 3, "file": "index.js", "sources": ["pages/country/index.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY291bnRyeS9pbmRleC52dWU"], "sourcesContent": ["：<template>\r\n  <view class=\"page-container\">\r\n    <view class=\"header-background\" :style=\"{ backgroundImage: `url(${headerBgUrl})` }\">\r\n      <view class=\"page-title-wrapper\">\r\n        <text class=\"page-title\">国别列表</text>\r\n      </view>\r\n\r\n      <view class=\"search-bar-wrapper\">\r\n        <uni-search-bar\r\n            class=\"search-bar\"\r\n            placeholder=\"搜索国别名称\"\r\n            v-model=\"searchKeyword\"\r\n            @confirm=\"onSearch\"\r\n            @cancel=\"onCancelSearch\"\r\n            @clear=\"onSearch\"\r\n            radius=\"100\"\r\n            bgColor=\"#ffffff\"\r\n        />\r\n      </view>\r\n\r\n      <view class=\"continent-tabs-container\">\r\n        <scroll-view class=\"continent-tabs\" scroll-x=\"true\" :show-scrollbar=\"false\"  @scroll=\"onTabsScroll\">\r\n          <view\r\n              v-for=\"(tab, index) in continents\"\r\n              :key=\"tab.value\"\r\n              :class=\"['tab-item', { active: activeContinent === tab.value }]\"\r\n              @click=\"onFilterTap(tab.value, index)\"\r\n              :id=\"'tab-' + index\"\r\n              :style=\"{ backgroundImage: activeContinent === tab.value ? `url(${activeTabBgUrl})` : 'none' }\"\r\n          >\r\n            {{ tab.label }}\r\n          </view>\r\n        </scroll-view>\r\n        <view class=\"active-indicator\" :style=\"{ left: indicatorLeft + 'px' }\"></view>\r\n      </view>\r\n    </view>\r\n\r\n    <scroll-view class=\"country-list-wrapper\" scroll-y @scrolltolower=\"loadMore\">\r\n      <view class=\"country-card\" v-for=\"item in countryList\" :key=\"item.id\" @click=\"goToDetail(item.id)\">\r\n        <image class=\"country-image\" :src=\"baseUrl + item.listCoverUrl\" mode=\"aspectFill\" />\r\n\r\n        <view class=\"country-info\">\r\n          <view class=\"info-top\">\r\n            <view class=\"name-line\">\r\n              <text class=\"name-cn\">{{ item.nameCn }}</text>\r\n              <text class=\"name-en\">{{ item.nameEn }}</text>\r\n            </view>\r\n            <text class=\"summary\">{{ item.summary }}</text>\r\n          </view>\r\n          <image class=\"country-flag\" :src=\"baseUrl + item.flagUrl\" mode=\"scaleToFill\" />\r\n        </view>\r\n      </view>\r\n\r\n      <view v-if=\"loading\" class=\"status-tip\">\r\n        <uni-load-more status=\"loading\" />\r\n      </view>\r\n      <view v-else-if=\"countryList.length === 0\" class=\"empty-message-container\">\r\n        <text class=\"empty-text\">暂无相关国家信息</text>\r\n      </view>\r\n\r\n      <view class=\"scroll-view-bottom-spacer\"></view>\r\n    </scroll-view>\r\n\r\n    <CustomTabBar :current=\"3\" />\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, nextTick, getCurrentInstance } from 'vue';\r\nimport { onPullDownRefresh, onLoad, onShow } from '@dcloudio/uni-app';\r\nimport { getCountryList } from '@/api/content/country.js'; // 引入API\r\nimport { IMAGE_BASE_URL } from '@/utils/config.js';\r\nimport CustomTabBar from '@/components/layout/CustomTabBar.vue';\r\n\r\n// 2. 【核心修改】将硬编码的 ref 替换为从 assets 读取的 computed 属性\r\nconst headerBgUrl = computed(() => assets.value.bg_country_list_header || '');\r\nconst activeTabBgUrl = computed(() => assets.value.bg_country_list_active_tab || '');\r\n\r\n\r\n\r\n// 基础配置和状态\r\nconst baseUrl = IMAGE_BASE_URL;\r\nconst searchKeyword = ref('');\r\nconst activeContinent = ref('ALL');\r\nconst countryList = ref([]);\r\nconst loading = ref(false);\r\nconst instance = getCurrentInstance();\r\nconst assets = ref(uni.getStorageSync('staticAssets') || {});\r\n\r\n\r\n// 大洲Tab数据\r\nconst continents = [\r\n  { label: '全部', value: 'ALL' },\r\n  { label: '亚洲', value: 'ASIA' },\r\n  { label: '欧洲', value: 'EUROPE' },\r\n  { label: '北美洲', value: 'NORTH_AMERICA' },\r\n  { label: '南美洲', value: 'SOUTH_AMERICA' },\r\n  { label: '非洲', value: 'AFRICA' },\r\n  { label: '大洋洲', value: 'OCEANIA' }\r\n];\r\n\r\n// --- 指示器逻辑所需的状态 ---\r\n// 当前激活Tab的索引\r\nconst activeIndex = ref(0);\r\n// 存储所有Tab初始布局信息的数组\r\nconst tabInitialPositions = ref([]);\r\n// Tab栏的水平滚动距离\r\nconst tabsScrollLeft = ref(0);\r\n\r\n\r\n/**\r\n * @description: 使用 computed 属性动态计算指示器的 left 值，以实现高性能跟随\r\n */\r\nconst indicatorLeft = computed(() => {\r\n  // 确保已获取到Tab的布局信息，否则返回一个屏幕外的值\r\n  if (!tabInitialPositions.value || tabInitialPositions.value.length === 0) {\r\n    return -999;\r\n  }\r\n\r\n  const activeTabInfo = tabInitialPositions.value[activeIndex.value];\r\n  if (!activeTabInfo) {\r\n    return -999;\r\n  }\r\n\r\n  // 指示器三角形宽度的一半 (20rpx)，需转换为px单位进行计算\r\n  const indicatorHalfWidth = uni.upx2px(20);\r\n\r\n  // 核心计算公式: (Tab的初始left - 当前滚动距离) + (Tab宽度 / 2) - (指示器宽度 / 2)\r\n  const finalLeft = (activeTabInfo.left - tabsScrollLeft.value) + (activeTabInfo.width / 2) - indicatorHalfWidth;\r\n\r\n  return finalLeft;\r\n});\r\n\r\n/**\r\n * @description: Tab滚动时的处理函数\r\n * @param {object} event - 滚动事件对象\r\n */\r\nconst onTabsScroll = (event) => {\r\n  tabsScrollLeft.value = event.detail.scrollLeft;\r\n};\r\n\r\n/**\r\n * @description: 在DOM加载后，一次性计算所有Tab的初始位置\r\n */\r\nconst calculateAllTabsPosition = () => {\r\n  nextTick(() => {\r\n    const query = uni.createSelectorQuery().in(instance);\r\n    query.selectAll('.tab-item').boundingClientRect(data => {\r\n      if (data && data.length) {\r\n        tabInitialPositions.value = data;\r\n      }\r\n    }).exec();\r\n  });\r\n};\r\n\r\n/**\r\n * @description: 获取国别列表数据\r\n * @param {boolean} isRefresh - 是否为刷新操作\r\n */\r\nconst fetchData = async (isRefresh = false) => {\r\n  if (loading.value) return;\r\n  loading.value = true;\r\n  try {\r\n    const res = await getCountryList({\r\n      continent: activeContinent.value,\r\n      keyword: searchKeyword.value\r\n    });\r\n    countryList.value = isRefresh ? res.data : [...countryList.value, ...res.data];\r\n  } catch (error) {\r\n    console.error('获取国别列表失败:', error);\r\n    uni.showToast({ title: '数据加载失败', icon: 'none' });\r\n  } finally {\r\n    loading.value = false;\r\n    if (isRefresh) {\r\n      uni.stopPullDownRefresh();\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * @description: 点击Tab切换筛选\r\n * @param {string} continentValue - 大洲的枚举值\r\n * @param {number} index - 被点击的Tab的索引\r\n */\r\nconst onFilterTap = (continentValue, index) => {\r\n  if (activeContinent.value === continentValue) return;\r\n\r\n  activeContinent.value = continentValue;\r\n  activeIndex.value = index; // 更新当前激活的索引\r\n\r\n  fetchData(true);\r\n};\r\n\r\n/**\r\n * @description: 搜索确认\r\n */\r\nconst onSearch = () => {\r\n  fetchData(true);\r\n};\r\n\r\n/**\r\n * @description: 取消搜索\r\n */\r\nconst onCancelSearch = () => {\r\n  searchKeyword.value = '';\r\n  fetchData(true);\r\n};\r\n\r\n/**\r\n * @description: 跳转到国别详情页\r\n * @param {number} countryId - 国家ID\r\n */\r\nconst goToDetail = (countryId) => {\r\n  uni.navigateTo({ url: `/pages_sub/pages_country/detail?id=${countryId}` });\r\n};\r\n\r\n/**\r\n * @description: 滚动到底部加载更多（此处为空实现，可根据需求补充）\r\n */\r\nconst loadMore = () => {};\r\n\r\n\r\n// --- 页面生命周期钩子 ---\r\n\r\nonLoad(() => {\r\n  fetchData(true);\r\n  // 延迟执行以确保DOM渲染完毕\r\n  setTimeout(() => {\r\n    calculateAllTabsPosition();\r\n  }, 150);\r\n});\r\n\r\nonShow(() => {\r\n  uni.hideTabBar();\r\n});\r\n\r\nonPullDownRefresh(() => {\r\n  fetchData(true);\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 页面总容器 */\r\n.page-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background-color: transparent;\r\n}\r\n\r\n/* 顶部背景区域 */\r\n.header-background {\r\n  height: 420rpx;\r\n  background-size: cover;\r\n  background-position: center;\r\n  background-repeat: no-repeat;\r\n  position: relative;\r\n  z-index: 10;\r\n}\r\n\r\n/* 页面标题容器 */\r\n.page-title-wrapper {\r\n  position: absolute;\r\n  top: 94rpx;\r\n  left: 0;\r\n  width: 750rpx;\r\n  height: 88rpx;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n.page-title {\r\n  font-size: 32rpx;\r\n  font-weight: 600;\r\n  color: #FFFFFF;\r\n}\r\n\r\n/* 搜索栏包裹容器 */\r\n.search-bar-wrapper {\r\n  position: absolute;\r\n  top: 182rpx;\r\n  left: 0;\r\n  width: 750rpx;\r\n  height: 88rpx;\r\n  box-sizing: border-box;\r\n}\r\n:deep(.uni-search-bar) {\r\n  margin: 14rpx 16rpx 14rpx 32rpx;\r\n  height: calc(100% - 28rpx);\r\n  padding: 0 !important;\r\n}\r\n:deep(.uni-search-bar__box) {\r\n  height: 100% !important;\r\n  justify-content: flex-start !important;\r\n}\r\n:deep(.uni-search-bar__text-placeholder) {\r\n  font-size: 28rpx !important;\r\n  color: #9B9A9A !important;\r\n}\r\n\r\n:deep(.uni-searchbar__cancel) {\r\n  /* 设置字体大小 (建议使用rpx以适应不同屏幕) */\r\n  font-size: 30rpx !important;\r\n  color: #FFFFFF !important;\r\n\r\n  /* 如果需要，还可以设置字体粗细 */\r\n  // font-weight: bold !important;\r\n}\r\n\r\n/* 扁扁圆圆的Tab，且有间隔 */\r\n.continent-tabs-container {\r\n  position: absolute;\r\n  top: 320rpx;\r\n  transform: translateY(-50%);\r\n  width: 100%;\r\n  padding: 0 30rpx;\r\n  box-sizing: border-box;\r\n  margin-top: 10rpx;\r\n}\r\n.continent-tabs {\r\n  white-space: nowrap;\r\n  &::-webkit-scrollbar {\r\n    display: none;\r\n  }\r\n}\r\n.tab-item {\r\n  display: inline-block;\r\n  padding: 12rpx 30rpx;\r\n  font-size: 28rpx;\r\n  color: #23232A;\r\n  border-radius: 30rpx;\r\n  background-color: #FFFFFF;\r\n  transition: all 0.3s;\r\n  background-size: cover;\r\n  background-position: center;\r\n  margin-right: 20rpx;\r\n\r\n  &:last-child {\r\n    margin-right: 0;\r\n  }\r\n\r\n  &.active {\r\n    color: #23232A;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n/* 突起的尖角指示器 */\r\n.active-indicator {\r\n  position: absolute;\r\n  bottom: -32rpx;\r\n  width: 0;\r\n  height: 0;\r\n  border-left: 20rpx solid transparent;\r\n  border-right: 20rpx solid transparent;\r\n  border-bottom: 25rpx solid #fff;\r\n  transition: left 0.3s ease;\r\n  z-index: 10;\r\n}\r\n\r\n/* 国别列表包裹容器 - 样式保持不变 */\r\n.country-list-wrapper {\r\n  flex: 1;\r\n  height: 0;\r\n  background-color: #ffffff;\r\n  position: relative;\r\n  z-index: 15; /* 提高层级 */\r\n  border-radius: 30rpx 30rpx 0 0; /* 使用简写 */\r\n  margin-top: -32rpx;\r\n  box-sizing: border-box;\r\n  overflow: hidden; /* 确保内容不溢出圆角 */\r\n}\r\n\r\n/* 国别卡片 - 样式保持不变 */\r\n.country-card {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%; /* 卡片宽度撑满容器 */\r\n  height: 272rpx;\r\n  padding: 0 30rpx;\r\n  background-color: #fff;\r\n  box-sizing: border-box;\r\n  /* 使用下边框作为卡片之间的分隔线 */\r\n  border-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n/* 卡片左侧封面图 */\r\n.country-image {\r\n  flex-shrink: 0;\r\n  width: 336rpx;\r\n  height: 192rpx;\r\n  border-radius: 16rpx;\r\n  /* [新增] 增加右边距，与右侧信息区隔开 */\r\n  margin-right: 24rpx;\r\n}\r\n\r\n/* 卡片右侧信息布局 */\r\n.country-info {\r\n  flex: 1;\r\n  min-width: 0;\r\n  /* [主要修改] 高度与图片保持一致(192rpx)，为'space-between'提供空间 */\r\n  height: 192rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  /* [关键属性] 将子元素推向容器的顶部和底部 */\r\n  justify-content: space-between;\r\n}\r\n\r\n/* 顶部信息块（国名+简介）*/\r\n.info-top {\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 国名行 */\r\n.name-line {\r\n  display: flex;\r\n  align-items: baseline;\r\n  /* [新增] 增加国名和简介之间的垂直间距 */\r\n  margin-bottom: 8rpx;\r\n\r\n  .name-cn {\r\n    font-size: 28rpx;\r\n    font-weight: bold;\r\n    color: #23232A;\r\n  }\r\n  .name-en {\r\n    font-size: 22rpx;\r\n    margin-left: 12rpx;\r\n    color: #9B9A9A;\r\n  }\r\n}\r\n\r\n/* 简介 */\r\n.summary {\r\n  font-size: 24rpx;\r\n  color: #23232A;\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-line-clamp: 2; /* 简介最多显示2行 */\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 国旗 */\r\n.country-flag {\r\n  /* [修改] 更新国旗尺寸 */\r\n  width: 60rpx;\r\n  height: 40rpx; /* 调整为更协调的比例 */\r\n  border-radius: 4rpx;\r\n  border: 1rpx solid #eee;\r\n  align-self: flex-start; /* 保持在左侧对齐 */\r\n}\r\n\r\n/* 状态提示 */\r\n.status-tip, .empty-message-container {\r\n  padding: 80rpx 0;\r\n  text-align: center;\r\n}\r\n.empty-text {\r\n  font-size: 28rpx;\r\n  color: #999;\r\n}\r\n.scroll-view-bottom-spacer {\r\n  height: 180rpx;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages/country/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["computed", "IMAGE_BASE_URL", "ref", "getCurrentInstance", "uni", "nextTick", "getCountryList", "onLoad", "onShow", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;;AAwEA,MAAM,eAAe,MAAW;;;;AAGhC,UAAM,cAAcA,cAAQ,SAAC,MAAM,OAAO,MAAM,0BAA0B,EAAE;AAC5E,UAAM,iBAAiBA,cAAQ,SAAC,MAAM,OAAO,MAAM,8BAA8B,EAAE;AAKnF,UAAM,UAAUC,aAAAA;AAChB,UAAM,gBAAgBC,cAAAA,IAAI,EAAE;AAC5B,UAAM,kBAAkBA,cAAAA,IAAI,KAAK;AACjC,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAC1B,UAAM,UAAUA,cAAAA,IAAI,KAAK;AACzB,UAAM,WAAWC,cAAkB,mBAAA;AACnC,UAAM,SAASD,cAAG,IAACE,cAAG,MAAC,eAAe,cAAc,KAAK,CAAA,CAAE;AAI3D,UAAM,aAAa;AAAA,MACjB,EAAE,OAAO,MAAM,OAAO,MAAO;AAAA,MAC7B,EAAE,OAAO,MAAM,OAAO,OAAQ;AAAA,MAC9B,EAAE,OAAO,MAAM,OAAO,SAAU;AAAA,MAChC,EAAE,OAAO,OAAO,OAAO,gBAAiB;AAAA,MACxC,EAAE,OAAO,OAAO,OAAO,gBAAiB;AAAA,MACxC,EAAE,OAAO,MAAM,OAAO,SAAU;AAAA,MAChC,EAAE,OAAO,OAAO,OAAO,UAAW;AAAA,IACpC;AAIA,UAAM,cAAcF,cAAAA,IAAI,CAAC;AAEzB,UAAM,sBAAsBA,cAAAA,IAAI,CAAA,CAAE;AAElC,UAAM,iBAAiBA,cAAAA,IAAI,CAAC;AAM5B,UAAM,gBAAgBF,cAAQ,SAAC,MAAM;AAEnC,UAAI,CAAC,oBAAoB,SAAS,oBAAoB,MAAM,WAAW,GAAG;AACxE,eAAO;AAAA,MACR;AAED,YAAM,gBAAgB,oBAAoB,MAAM,YAAY,KAAK;AACjE,UAAI,CAAC,eAAe;AAClB,eAAO;AAAA,MACR;AAGD,YAAM,qBAAqBI,cAAAA,MAAI,OAAO,EAAE;AAGxC,YAAM,YAAa,cAAc,OAAO,eAAe,QAAU,cAAc,QAAQ,IAAK;AAE5F,aAAO;AAAA,IACT,CAAC;AAMD,UAAM,eAAe,CAAC,UAAU;AAC9B,qBAAe,QAAQ,MAAM,OAAO;AAAA,IACtC;AAKA,UAAM,2BAA2B,MAAM;AACrCC,oBAAAA,WAAS,MAAM;AACb,cAAM,QAAQD,cAAAA,MAAI,oBAAqB,EAAC,GAAG,QAAQ;AACnD,cAAM,UAAU,WAAW,EAAE,mBAAmB,UAAQ;AACtD,cAAI,QAAQ,KAAK,QAAQ;AACvB,gCAAoB,QAAQ;AAAA,UAC7B;AAAA,QACP,CAAK,EAAE,KAAI;AAAA,MACX,CAAG;AAAA,IACH;AAMA,UAAM,YAAY,OAAO,YAAY,UAAU;AAC7C,UAAI,QAAQ;AAAO;AACnB,cAAQ,QAAQ;AAChB,UAAI;AACF,cAAM,MAAM,MAAME,mCAAe;AAAA,UAC/B,WAAW,gBAAgB;AAAA,UAC3B,SAAS,cAAc;AAAA,QAC7B,CAAK;AACD,oBAAY,QAAQ,YAAY,IAAI,OAAO,CAAC,GAAG,YAAY,OAAO,GAAG,IAAI,IAAI;AAAA,MAC9E,SAAQ,OAAO;AACdF,sBAAc,MAAA,MAAA,SAAA,kCAAA,aAAa,KAAK;AAChCA,sBAAG,MAAC,UAAU,EAAE,OAAO,UAAU,MAAM,OAAM,CAAE;AAAA,MACnD,UAAY;AACR,gBAAQ,QAAQ;AAChB,YAAI,WAAW;AACbA,wBAAG,MAAC,oBAAmB;AAAA,QACxB;AAAA,MACF;AAAA,IACH;AAOA,UAAM,cAAc,CAAC,gBAAgB,UAAU;AAC7C,UAAI,gBAAgB,UAAU;AAAgB;AAE9C,sBAAgB,QAAQ;AACxB,kBAAY,QAAQ;AAEpB,gBAAU,IAAI;AAAA,IAChB;AAKA,UAAM,WAAW,MAAM;AACrB,gBAAU,IAAI;AAAA,IAChB;AAKA,UAAM,iBAAiB,MAAM;AAC3B,oBAAc,QAAQ;AACtB,gBAAU,IAAI;AAAA,IAChB;AAMA,UAAM,aAAa,CAAC,cAAc;AAChCA,oBAAG,MAAC,WAAW,EAAE,KAAK,sCAAsC,SAAS,GAAE,CAAE;AAAA,IAC3E;AAKA,UAAM,WAAW,MAAM;AAAA,IAAA;AAKvBG,kBAAAA,OAAO,MAAM;AACX,gBAAU,IAAI;AAEd,iBAAW,MAAM;AACf;MACD,GAAE,GAAG;AAAA,IACR,CAAC;AAEDC,kBAAAA,OAAO,MAAM;AACXJ,oBAAG,MAAC,WAAU;AAAA,IAChB,CAAC;AAEDK,kBAAAA,kBAAkB,MAAM;AACtB,gBAAU,IAAI;AAAA,IAChB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7OD,GAAG,WAAW,eAAe;"}