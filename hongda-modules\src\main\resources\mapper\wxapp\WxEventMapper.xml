<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongda.wxapp.mapper.WxEventMapper">

    <!-- 获取不重复地区列表 -->
    <select id="selectDistinctLocations" resultType="String">
        SELECT DISTINCT location 
        FROM hongda_event 
        WHERE location IS NOT NULL 
        AND location != ''
        ORDER BY location
    </select>

    <!-- 地区统计 -->
    <select id="selectLocationStatistics" resultType="map">
        SELECT 
            location as locationName,
            COUNT(*) as eventCount
        FROM hongda_event 
        WHERE location IS NOT NULL 
        AND location != ''
        GROUP BY location
        ORDER BY eventCount DESC
    </select>

    <!-- 获取不重复城市列表 -->
    <select id="selectDistinctCities" resultType="String">
        SELECT DISTINCT city 
        FROM hongda_event 
        WHERE city IS NOT NULL 
        AND city != ''
        ORDER BY city
    </select>

</mapper> 