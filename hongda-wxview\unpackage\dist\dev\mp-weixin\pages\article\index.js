"use strict";
const common_vendor = require("../../common/vendor.js");
const api_content_article = require("../../api/content/article.js");
const api_content_tag = require("../../api/content/tag.js");
const utils_image = require("../../utils/image.js");
const api_content_region = require("../../api/content/region.js");
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_uni_easyinput2 = common_vendor.resolveComponent("uni-easyinput");
  const _easycom_u_tabs2 = common_vendor.resolveComponent("u-tabs");
  const _easycom_u_loading_icon2 = common_vendor.resolveComponent("u-loading-icon");
  const _easycom_u_image2 = common_vendor.resolveComponent("u-image");
  const _easycom_u_empty2 = common_vendor.resolveComponent("u-empty");
  const _easycom_u_loadmore2 = common_vendor.resolveComponent("u-loadmore");
  (_easycom_u_icon2 + _easycom_uni_easyinput2 + _easycom_u_tabs2 + _easycom_u_loading_icon2 + _easycom_u_image2 + _easycom_u_empty2 + _easycom_u_loadmore2)();
}
const _easycom_u_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_uni_easyinput = () => "../../uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.js";
const _easycom_u_tabs = () => "../../uni_modules/uview-plus/components/u-tabs/u-tabs.js";
const _easycom_u_loading_icon = () => "../../uni_modules/uview-plus/components/u-loading-icon/u-loading-icon.js";
const _easycom_u_image = () => "../../uni_modules/uview-plus/components/u-image/u-image.js";
const _easycom_u_empty = () => "../../uni_modules/uview-plus/components/u-empty/u-empty.js";
const _easycom_u_loadmore = () => "../../uni_modules/uview-plus/components/u-loadmore/u-loadmore.js";
if (!Math) {
  (_easycom_u_icon + _easycom_uni_easyinput + _easycom_u_tabs + _easycom_u_loading_icon + _easycom_u_image + _easycom_u_empty + _easycom_u_loadmore + CustomTabBar)();
}
const CustomTabBar = () => "../../components/layout/CustomTabBar.js";
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const articleList = common_vendor.ref([]);
    const tagList = common_vendor.ref([{ id: null, name: "全部" }]);
    const currentTabIndex = common_vendor.ref(0);
    const loadStatus = common_vendor.ref("loadmore");
    const searchKeyword = common_vendor.ref("");
    const activePanel = common_vendor.ref(null);
    const assets = common_vendor.ref({});
    const queryParams = common_vendor.reactive({
      pageNum: 1,
      pageSize: 10,
      title: null,
      tagIds: null,
      orderByColumn: "sort_order",
      isAsc: "asc",
      status: "1",
      "params[beginPublishTime]": null,
      "params[endPublishTime]": null,
      "params[region]": null
    });
    const tempFilters = common_vendor.reactive({
      sort: "default",
      region: "all",
      time: "all"
    });
    const appliedFilters = common_vendor.reactive({
      sort: "default",
      region: "all",
      time: "all"
    });
    const sortActions = [
      { name: "默认排序", value: "default" },
      { name: "最新发布", value: "publish_time" },
      { name: "热度排序", value: "view_count" }
    ];
    const regionActions = common_vendor.ref([]);
    const timeActions = [
      { name: "不限时间", value: "all" },
      { name: "最近一周", value: "week" },
      { name: "最近一月", value: "month" },
      { name: "最近一年", value: "year" }
    ];
    const headerStyle = common_vendor.computed(() => {
      const imageUrl = assets.value.bg_article_header;
      if (imageUrl) {
        return {
          backgroundImage: `url('${imageUrl}')`
        };
      }
      return {};
    });
    const sortButtonText = common_vendor.computed(() => {
      const sortItem = sortActions.find((item) => item.value === appliedFilters.sort);
      return sortItem ? sortItem.name : "默认排序";
    });
    const isFilterActive = common_vendor.computed(() => appliedFilters.region !== "all" || appliedFilters.time !== "all");
    const filterButtonText = common_vendor.computed(() => {
      if (!isFilterActive.value) {
        return "筛选";
      }
      let count = 0;
      if (appliedFilters.region !== "all")
        count++;
      if (appliedFilters.time !== "all")
        count++;
      return `筛选(${count})`;
    });
    const togglePanel = (panelName) => {
      if (activePanel.value === panelName) {
        activePanel.value = null;
      } else {
        tempFilters.sort = appliedFilters.sort;
        tempFilters.region = appliedFilters.region;
        tempFilters.time = appliedFilters.time;
        activePanel.value = panelName;
      }
    };
    const closePanel = () => {
      activePanel.value = null;
    };
    const handleSortSelect = (sort) => {
      appliedFilters.sort = sort.value;
      queryParams.orderByColumn = sort.value === "default" ? "sort_order" : sort.value;
      queryParams.isAsc = sort.value === "default" ? "asc" : "desc";
      closePanel();
      loadArticles(true);
    };
    const handleFilterReset = () => {
      appliedFilters.region = "all";
      appliedFilters.time = "all";
      tempFilters.region = "all";
      tempFilters.time = "all";
      updateFilterParams();
      closePanel();
      loadArticles(true);
    };
    const handleFilterConfirm = () => {
      appliedFilters.region = tempFilters.region;
      appliedFilters.time = tempFilters.time;
      updateFilterParams();
      closePanel();
      loadArticles(true);
    };
    const updateFilterParams = () => {
      queryParams["params[region]"] = appliedFilters.region === "all" ? null : appliedFilters.region;
      const now = /* @__PURE__ */ new Date();
      let beginDate = null;
      if (appliedFilters.time !== "all") {
        const endDate = /* @__PURE__ */ new Date();
        if (appliedFilters.time === "week")
          endDate.setDate(now.getDate() - 7);
        else if (appliedFilters.time === "month")
          endDate.setMonth(now.getMonth() - 1);
        else if (appliedFilters.time === "year")
          endDate.setFullYear(now.getFullYear() - 1);
        beginDate = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, "0")}-${String(endDate.getDate()).padStart(2, "0")}`;
      }
      queryParams["params[beginPublishTime]"] = beginDate;
    };
    const parseArticles = (rows) => {
      return rows.map((article) => {
        let parsedTags = [];
        if (article.tags && typeof article.tags === "string") {
          try {
            parsedTags = JSON.parse(article.tags);
          } catch (e) {
            common_vendor.index.__f__("error", "at pages/article/index.vue:321", "解析文章标签JSON失败:", article.id, article.tags, e);
          }
        }
        return { ...article, parsedTags: Array.isArray(parsedTags) ? parsedTags : [] };
      });
    };
    const loadArticles = async (isRefresh = false) => {
      if (!isRefresh && loadStatus.value === "nomore")
        return;
      if (isRefresh) {
        queryParams.pageNum = 1;
        articleList.value = [];
      }
      loadStatus.value = "loading";
      try {
        const response = await api_content_article.getArticleList(queryParams);
        const newArticles = parseArticles(response.rows);
        if (isRefresh) {
          articleList.value = newArticles;
        } else {
          articleList.value.push(...newArticles);
        }
        if (response.rows.length < queryParams.pageSize || articleList.value.length >= response.total) {
          loadStatus.value = "nomore";
        } else {
          loadStatus.value = "loadmore";
        }
      } catch (error) {
        loadStatus.value = "loadmore";
        common_vendor.index.__f__("error", "at pages/article/index.vue:350", "加载文章列表失败:", error);
        common_vendor.index.showToast({ title: "加载失败", icon: "none" });
      } finally {
        common_vendor.index.stopPullDownRefresh();
      }
    };
    const loadTags = async () => {
      try {
        const response = await api_content_tag.listAllTag();
        const backendTags = Array.isArray(response.data) ? response.data : [];
        tagList.value = [{ id: null, name: "全部" }, ...backendTags];
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/article/index.vue:363", "加载标签列表失败:", error);
      }
    };
    const loadRegions = async () => {
      try {
        const response = await api_content_region.listAllRegion();
        const formattedRegions = response.data.map((item) => ({
          name: item.name,
          value: item.code
        }));
        regionActions.value = [{ name: "全部地区", value: "all" }, ...formattedRegions];
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/article/index.vue:376", "加载地区列表失败:", error);
        regionActions.value = [{ name: "全部地区", value: "all" }];
      }
    };
    const handleTabChange = (tab) => {
      currentTabIndex.value = tab.index;
      queryParams.tagIds = tab.id === null ? null : String(tab.id);
      loadArticles(true);
    };
    const onSearchInput = (value) => {
      searchKeyword.value = value;
      if (!value || value.trim() === "") {
        handleClear();
      }
    };
    const handleSearch = () => {
      const keyword = searchKeyword.value.trim();
      queryParams.title = keyword || null;
      loadArticles(true);
    };
    const handleClear = () => {
      searchKeyword.value = "";
      queryParams.title = null;
      loadArticles(true);
      setTimeout(() => common_vendor.index.hideKeyboard(), 300);
    };
    const onImageLoad = () => {
    };
    const onImageError = () => {
    };
    const gotoDetail = (id) => {
      common_vendor.index.navigateTo({
        url: `/pages_sub/pages_article/detail?id=${id}`
      });
    };
    const formatDate = (dateString) => {
      if (!dateString)
        return "";
      return dateString.split(" ")[0];
    };
    const loadMore = () => {
      if (loadStatus.value === "loadmore") {
        queryParams.pageNum++;
        loadArticles();
      }
    };
    const initPageData = async () => {
      try {
        await Promise.all([
          loadTags(),
          loadRegions(),
          loadArticles(true)
        ]);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/article/index.vue:436", "页面初始化失败:", error);
        common_vendor.index.showToast({ title: "页面加载失败", icon: "none" });
      }
    };
    common_vendor.onMounted(() => {
      assets.value = common_vendor.index.getStorageSync("staticAssets") || {};
      initPageData();
    });
    common_vendor.onShow(() => {
      common_vendor.index.hideTabBar();
    });
    common_vendor.onPullDownRefresh(() => {
      loadArticles(true);
    });
    common_vendor.onReachBottom(() => {
      loadMore();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(sortButtonText.value),
        b: common_vendor.p({
          name: "arrow-down-fill",
          color: "#FFFFFF",
          size: "10"
        }),
        c: activePanel.value === "sort" ? 1 : "",
        d: common_vendor.o(($event) => togglePanel("sort")),
        e: common_vendor.t(filterButtonText.value),
        f: common_vendor.p({
          name: "arrow-down-fill",
          color: "#FFFFFF",
          size: "10"
        }),
        g: isFilterActive.value
      }, isFilterActive.value ? {} : {}, {
        h: activePanel.value === "filter" || isFilterActive.value ? 1 : "",
        i: common_vendor.o(($event) => togglePanel("filter")),
        j: common_vendor.o(handleSearch),
        k: common_vendor.o(handleClear),
        l: common_vendor.o(onSearchInput),
        m: common_vendor.o(($event) => searchKeyword.value = $event),
        n: common_vendor.p({
          ["suffix-icon"]: "search",
          placeholder: "搜索资讯",
          clearable: true,
          modelValue: searchKeyword.value
        }),
        o: common_vendor.s(headerStyle.value),
        p: tagList.value.length > 1
      }, tagList.value.length > 1 ? {
        q: common_vendor.o(handleTabChange),
        r: common_vendor.p({
          list: tagList.value,
          current: currentTabIndex.value,
          keyName: "name",
          lineColor: "#023F98",
          lineHeight: "6",
          lineWidth: "auto",
          activeStyle: {
            color: "#023F98",
            fontSize: "32rpx",
            fontWeight: "bold"
          },
          inactiveStyle: {
            color: "#9B9A9A",
            fontSize: "32rpx",
            fontWeight: "normal"
          }
        })
      } : {}, {
        s: common_vendor.f(articleList.value, (item, k0, i0) => {
          return common_vendor.e({
            a: "fd1fc04a-5-" + i0 + "," + ("fd1fc04a-4-" + i0),
            b: "fd1fc04a-6-" + i0 + "," + ("fd1fc04a-4-" + i0),
            c: common_vendor.o(onImageError, item.id),
            d: common_vendor.o(onImageLoad, item.id),
            e: "fd1fc04a-4-" + i0,
            f: common_vendor.p({
              src: common_vendor.unref(utils_image.getFullImageUrl)(item.coverImageUrl),
              width: "100%",
              height: "190rpx",
              radius: "16",
              fade: true,
              ["lazy-load"]: true
            }),
            g: common_vendor.t(item.title),
            h: item.parsedTags && item.parsedTags.length > 0
          }, item.parsedTags && item.parsedTags.length > 0 ? {
            i: common_vendor.f(item.parsedTags, (tag, k1, i1) => {
              return {
                a: common_vendor.t(tag.name),
                b: tag.id
              };
            })
          } : {}, {
            j: common_vendor.t(item.source),
            k: common_vendor.t(formatDate(item.publishTime)),
            l: item.id,
            m: common_vendor.o(($event) => gotoDetail(item.id), item.id)
          });
        }),
        t: common_vendor.p({
          color: "#667eea",
          size: "20"
        }),
        v: common_vendor.p({
          name: "photo-off",
          color: "#9ca3af",
          size: "24"
        }),
        w: loadStatus.value === "nomore" && articleList.value.length === 0
      }, loadStatus.value === "nomore" && articleList.value.length === 0 ? {
        x: common_vendor.p({
          mode: "news",
          text: "暂无资讯",
          marginTop: "100"
        })
      } : {
        y: common_vendor.p({
          status: loadStatus.value,
          line: "true"
        })
      }, {
        z: common_vendor.o(loadMore),
        A: activePanel.value
      }, activePanel.value ? common_vendor.e({
        B: common_vendor.o(closePanel),
        C: activePanel.value === "sort"
      }, activePanel.value === "sort" ? {
        D: common_vendor.f(sortActions, (sort, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(sort.name),
            b: tempFilters.sort === sort.value
          }, tempFilters.sort === sort.value ? {
            c: "fd1fc04a-9-" + i0,
            d: common_vendor.p({
              name: "checkmark",
              color: "#023F98",
              size: "18"
            })
          } : {}, {
            e: sort.value,
            f: tempFilters.sort === sort.value ? 1 : "",
            g: common_vendor.o(($event) => handleSortSelect(sort), sort.value)
          });
        })
      } : {}, {
        E: activePanel.value === "filter"
      }, activePanel.value === "filter" ? {
        F: common_vendor.f(regionActions.value, (region, k0, i0) => {
          return {
            a: common_vendor.t(region.name),
            b: region.value,
            c: tempFilters.region === region.value ? 1 : "",
            d: common_vendor.o(($event) => tempFilters.region = region.value, region.value)
          };
        }),
        G: common_vendor.f(timeActions, (time, k0, i0) => {
          return {
            a: common_vendor.t(time.name),
            b: time.value,
            c: tempFilters.time === time.value ? 1 : "",
            d: common_vendor.o(($event) => tempFilters.time = time.value, time.value)
          };
        }),
        H: common_vendor.o(handleFilterReset),
        I: common_vendor.o(handleFilterConfirm)
      } : {}, {
        J: activePanel.value ? 1 : ""
      }) : {}, {
        K: common_vendor.p({
          current: 1
        })
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-fd1fc04a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/article/index.js.map
