{"version": 3, "file": "index.js", "sources": ["pages/article/index.vue", "../../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYXJ0aWNsZS9pbmRleC52dWU"], "sourcesContent": ["<template>\r\n  <view class=\"page-container\">\r\n\r\n    <!-- 【修改】在这里通过 :style 动态绑定背景 -->\r\n    <view class=\"header-section\" :style=\"headerStyle\">\r\n      <view class=\"custom-nav-bar\">\r\n        <view class=\"status-bar\"></view>\r\n        <view class=\"nav-title\">资讯列表</view>\r\n        <view class=\"filter-bar\">\r\n          <view class=\"sort-button\" :class=\"{ 'is-active': activePanel === 'sort' }\" @click=\"togglePanel('sort')\">\r\n            {{ sortButtonText }}\r\n            <u-icon name=\"arrow-down-fill\" color=\"#FFFFFF\" size=\"10\"></u-icon>\r\n          </view>\r\n          <view class=\"filter-button\" :class=\"{ 'is-active': activePanel === 'filter' || isFilterActive }\"\r\n                @click=\"togglePanel('filter')\">\r\n            {{ filterButtonText }}\r\n            <u-icon name=\"arrow-down-fill\" color=\"#FFFFFF\" size=\"10\"></u-icon>\r\n            <view class=\"active-dot\" v-if=\"isFilterActive\"></view>\r\n          </view>\r\n          <view class=\"search-box\">\r\n            <uni-easyinput\r\n                class=\"search-input\"\r\n                suffix-icon=\"search\"\r\n                v-model=\"searchKeyword\"\r\n                placeholder=\"搜索资讯\"\r\n                :clearable=\"true\"\r\n                @confirm=\"handleSearch\"\r\n                @clear=\"handleClear\"\r\n                @input=\"onSearchInput\"\r\n            ></uni-easyinput>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"content-section\">\r\n      <view class=\"tabs-container\">\r\n        <u-tabs\r\n            v-if=\"tagList.length > 1\"\r\n            :list=\"tagList\"\r\n            :current=\"currentTabIndex\"\r\n            keyName=\"name\"\r\n            @change=\"handleTabChange\"\r\n            lineColor=\"#023F98\"\r\n            lineHeight=\"6\"\r\n            lineWidth=\"auto\"\r\n            :activeStyle=\"{\r\n              color: '#023F98',\r\n              fontSize: '32rpx',\r\n              fontWeight: 'bold'\r\n            }\"\r\n            :inactiveStyle=\"{\r\n              color: '#9B9A9A',\r\n              fontSize: '32rpx',\r\n              fontWeight: 'normal'\r\n            }\"\r\n        ></u-tabs>\r\n      </view>\r\n\r\n      <scroll-view scroll-y class=\"article-list-scroll\" @scrolltolower=\"loadMore\" enable-flex>\r\n        <view class=\"article-list\">\r\n          <view class=\"article-card\" v-for=\"item in articleList\" :key=\"item.id\" @click=\"gotoDetail(item.id)\">\r\n            <view class=\"card-cover\">\r\n              <u-image\r\n                  :src=\"getFullImageUrl(item.coverImageUrl)\"\r\n                  width=\"100%\"\r\n                  height=\"190rpx\"\r\n                  radius=\"16\"\r\n                  :fade=\"true\"\r\n                  :lazy-load=\"true\"\r\n                  @error=\"onImageError\"\r\n                  @load=\"onImageLoad\"\r\n              >\r\n                <template v-slot:loading>\r\n                  <view class=\"image-loading\">\r\n                    <u-loading-icon color=\"#667eea\" size=\"20\"></u-loading-icon>\r\n                    <text class=\"loading-text\">加载中...</text>\r\n                  </view>\r\n                </template>\r\n                <template v-slot:error>\r\n                  <view class=\"image-error\">\r\n                    <u-icon name=\"photo-off\" color=\"#9ca3af\" size=\"24\"></u-icon>\r\n                    <text class=\"error-text\">图片加载失败</text>\r\n                  </view>\r\n                </template>\r\n              </u-image>\r\n            </view>\r\n            <view class=\"card-content\">\r\n              <text class=\"card-title\">{{ item.title }}</text>\r\n              <view class=\"card-tags\" v-if=\"item.parsedTags && item.parsedTags.length > 0\">\r\n                <text class=\"tag-item\" v-for=\"tag in item.parsedTags\" :key=\"tag.id\">{{ tag.name }}</text>\r\n              </view>\r\n              <view class=\"card-meta\">\r\n                <text class=\"meta-source\">{{ item.source }}</text>\r\n                <text class=\"meta-date\">{{ formatDate(item.publishTime) }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <u-empty v-if=\"loadStatus === 'nomore' && articleList.length === 0\"\r\n                 mode=\"news\" text=\"暂无资讯\"\r\n                 marginTop=\"100\"></u-empty>\r\n        <u-loadmore v-else :status=\"loadStatus\" line=\"true\"/>\r\n      </scroll-view>\r\n    </view>\r\n\r\n    <view class=\"dropdown-wrapper\" v-if=\"activePanel\">\r\n      <view class=\"dropdown-mask\" @click=\"closePanel\"></view>\r\n      <view class=\"dropdown-panel\" :class=\"{ show: activePanel }\">\r\n        <view v-if=\"activePanel === 'sort'\" class=\"sort-panel\">\r\n          <view\r\n              class=\"sort-option\"\r\n              v-for=\"sort in sortActions\"\r\n              :key=\"sort.value\"\r\n              :class=\"{ 'active': tempFilters.sort === sort.value }\"\r\n              @click=\"handleSortSelect(sort)\"\r\n          >\r\n            {{ sort.name }}\r\n            <u-icon v-if=\"tempFilters.sort === sort.value\" name=\"checkmark\" color=\"#023F98\" size=\"18\"></u-icon>\r\n          </view>\r\n        </view>\r\n\r\n        <view v-if=\"activePanel === 'filter'\" class=\"filter-panel\">\r\n          <scroll-view scroll-y class=\"filter-scroll\">\r\n            <view class=\"panel-section\">\r\n              <text class=\"section-title\">内容地区</text>\r\n              <view class=\"panel-options\">\r\n                <view\r\n                    class=\"option-btn\"\r\n                    v-for=\"region in regionActions\"\r\n                    :key=\"region.value\"\r\n                    :class=\"{ 'active': tempFilters.region === region.value }\"\r\n                    @click=\"tempFilters.region = region.value\"\r\n                >{{ region.name }}\r\n                </view>\r\n              </view>\r\n            </view>\r\n            <view class=\"panel-section\">\r\n              <text class=\"section-title\">发布时间</text>\r\n              <view class=\"panel-options\">\r\n                <view\r\n                    class=\"option-btn\"\r\n                    v-for=\"time in timeActions\"\r\n                    :key=\"time.value\"\r\n                    :class=\"{ 'active': tempFilters.time === time.value }\"\r\n                    @click=\"tempFilters.time = time.value\"\r\n                >{{ time.name }}\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </scroll-view>\r\n          <view class=\"panel-footer\">\r\n            <button class=\"footer-btn reset\" @click=\"handleFilterReset\">重置</button>\r\n            <button class=\"footer-btn confirm\" @click=\"handleFilterConfirm\">确定</button>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <CustomTabBar :current=\"1\"/>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted, computed } from 'vue';\r\nimport CustomTabBar from '@/components/layout/CustomTabBar.vue'; // 修正了导入路径\r\nimport { onPullDownRefresh, onReachBottom, onShow } from '@dcloudio/uni-app';\r\nimport { getArticleList } from '@/api/content/article.js';\r\nimport { listAllTag } from '@/api/content/tag.js';\r\nimport { getFullImageUrl } from '@/utils/image.js';\r\nimport { listAllRegion } from \"@/api/content/region\";\r\n\r\n// --- 响应式状态 ---\r\nconst articleList = ref([]);\r\nconst tagList = ref([{ id: null, name: '全部' }]);\r\nconst currentTabIndex = ref(0);\r\nconst loadStatus = ref('loadmore');\r\nconst searchKeyword = ref('');\r\nconst activePanel = ref(null);\r\nconst assets = ref({}); // 【新增】用于存储静态资源\r\n\r\n// --- 查询参数 ---\r\nconst queryParams = reactive({\r\n  pageNum: 1,\r\n  pageSize: 10,\r\n  title: null,\r\n  tagIds: null,\r\n  orderByColumn: 'sort_order',\r\n  isAsc: 'asc',\r\n  status: '1',\r\n  'params[beginPublishTime]': null,\r\n  'params[endPublishTime]': null,\r\n  'params[region]': null,\r\n});\r\n\r\n// --- 筛选状态管理 ---\r\nconst tempFilters = reactive({\r\n  sort: 'default',\r\n  region: 'all',\r\n  time: 'all',\r\n});\r\n\r\nconst appliedFilters = reactive({\r\n  sort: 'default',\r\n  region: 'all',\r\n  time: 'all',\r\n});\r\n\r\n// --- 筛选选项静态配置 ---\r\nconst sortActions = [\r\n  { name: '默认排序', value: 'default' },\r\n  { name: '最新发布', value: 'publish_time' },\r\n  { name: '热度排序', value: 'view_count' },\r\n];\r\n\r\nconst regionActions = ref([]);\r\n\r\nconst timeActions = [\r\n  { name: '不限时间', value: 'all' },\r\n  { name: '最近一周', value: 'week' },\r\n  { name: '最近一月', value: 'month' },\r\n  { name: '最近一年', value: 'year' },\r\n];\r\n\r\n// --- Computed Properties for UI ---\r\n\r\n// 【新增】动态计算头部背景样式\r\nconst headerStyle = computed(() => {\r\n  // 从缓存中获取URL，这里不需要备用方案\r\n  const imageUrl = assets.value.bg_article_header;\r\n  if (imageUrl) {\r\n    return {\r\n      backgroundImage: `url('${imageUrl}')`\r\n    };\r\n  }\r\n  // 如果没有配置，则返回一个空对象，不应用任何背景\r\n  return {};\r\n});\r\n\r\nconst sortButtonText = computed(() => {\r\n  const sortItem = sortActions.find(item => item.value === appliedFilters.sort);\r\n  return sortItem ? sortItem.name : '默认排序';\r\n});\r\n\r\nconst isFilterActive = computed(() => appliedFilters.region !== 'all' || appliedFilters.time !== 'all');\r\n\r\nconst filterButtonText = computed(() => {\r\n  if (!isFilterActive.value) {\r\n    return '筛选';\r\n  }\r\n  let count = 0;\r\n  if (appliedFilters.region !== 'all') count++;\r\n  if (appliedFilters.time !== 'all') count++;\r\n  return `筛选(${count})`;\r\n});\r\n\r\n// --- Methods ---\r\nconst togglePanel = (panelName) => {\r\n  if (activePanel.value === panelName) {\r\n    activePanel.value = null;\r\n  } else {\r\n    tempFilters.sort = appliedFilters.sort;\r\n    tempFilters.region = appliedFilters.region;\r\n    tempFilters.time = appliedFilters.time;\r\n    activePanel.value = panelName;\r\n  }\r\n};\r\n\r\nconst closePanel = () => {\r\n  activePanel.value = null;\r\n};\r\n\r\nconst handleSortSelect = (sort) => {\r\n  appliedFilters.sort = sort.value;\r\n  queryParams.orderByColumn = sort.value === 'default' ? 'sort_order' : sort.value;\r\n  queryParams.isAsc = sort.value === 'default' ? 'asc' : 'desc';\r\n  closePanel();\r\n  loadArticles(true);\r\n};\r\n\r\nconst handleFilterReset = () => {\r\n  appliedFilters.region = 'all';\r\n  appliedFilters.time = 'all';\r\n  tempFilters.region = 'all';\r\n  tempFilters.time = 'all';\r\n  updateFilterParams();\r\n  closePanel();\r\n  loadArticles(true);\r\n};\r\n\r\nconst handleFilterConfirm = () => {\r\n  appliedFilters.region = tempFilters.region;\r\n  appliedFilters.time = tempFilters.time;\r\n  updateFilterParams();\r\n  closePanel();\r\n  loadArticles(true);\r\n};\r\n\r\nconst updateFilterParams = () => {\r\n  queryParams['params[region]'] = appliedFilters.region === 'all' ? null : appliedFilters.region;\r\n  const now = new Date();\r\n  let beginDate = null;\r\n  if (appliedFilters.time !== 'all') {\r\n    const endDate = new Date();\r\n    if (appliedFilters.time === 'week') endDate.setDate(now.getDate() - 7);\r\n    else if (appliedFilters.time === 'month') endDate.setMonth(now.getMonth() - 1);\r\n    else if (appliedFilters.time === 'year') endDate.setFullYear(now.getFullYear() - 1);\r\n    beginDate = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}-${String(endDate.getDate()).padStart(2, '0')}`;\r\n  }\r\n  queryParams['params[beginPublishTime]'] = beginDate;\r\n};\r\n\r\nconst parseArticles = (rows) => {\r\n  return rows.map((article) => {\r\n    let parsedTags = [];\r\n    if (article.tags && typeof article.tags === 'string') {\r\n      try {\r\n        parsedTags = JSON.parse(article.tags);\r\n      } catch (e) {\r\n        console.error('解析文章标签JSON失败:', article.id, article.tags, e);\r\n      }\r\n    }\r\n    return { ...article, parsedTags: Array.isArray(parsedTags) ? parsedTags : [] };\r\n  });\r\n};\r\n\r\nconst loadArticles = async (isRefresh = false) => {\r\n  if (!isRefresh && loadStatus.value === 'nomore') return;\r\n  if (isRefresh) {\r\n    queryParams.pageNum = 1;\r\n    articleList.value = [];\r\n  }\r\n  loadStatus.value = 'loading';\r\n  try {\r\n    const response = await getArticleList(queryParams);\r\n    const newArticles = parseArticles(response.rows);\r\n    if (isRefresh) {\r\n      articleList.value = newArticles;\r\n    } else {\r\n      articleList.value.push(...newArticles);\r\n    }\r\n    if (response.rows.length < queryParams.pageSize || articleList.value.length >= response.total) {\r\n      loadStatus.value = 'nomore';\r\n    } else {\r\n      loadStatus.value = 'loadmore';\r\n    }\r\n  } catch (error) {\r\n    loadStatus.value = 'loadmore';\r\n    console.error('加载文章列表失败:', error);\r\n    uni.showToast({ title: '加载失败', icon: 'none' });\r\n  } finally {\r\n    uni.stopPullDownRefresh();\r\n  }\r\n};\r\n\r\nconst loadTags = async () => {\r\n  try {\r\n    const response = await listAllTag();\r\n    const backendTags = Array.isArray(response.data) ? response.data : [];\r\n    tagList.value = [{ id: null, name: '全部' }, ...backendTags];\r\n  } catch (error) {\r\n    console.error('加载标签列表失败:', error);\r\n  }\r\n};\r\n\r\nconst loadRegions = async () => {\r\n  try {\r\n    const response = await listAllRegion();\r\n    const formattedRegions = response.data.map(item => ({\r\n      name: item.name,\r\n      value: item.code\r\n    }));\r\n    regionActions.value = [{ name: '全部地区', value: 'all' }, ...formattedRegions];\r\n  } catch (error) {\r\n    console.error('加载地区列表失败:', error);\r\n    regionActions.value = [{ name: '全部地区', value: 'all' }];\r\n  }\r\n};\r\n\r\nconst handleTabChange = (tab) => {\r\n  currentTabIndex.value = tab.index;\r\n  queryParams.tagIds = tab.id === null ? null : String(tab.id);\r\n  loadArticles(true);\r\n};\r\n\r\nconst onSearchInput = (value) => {\r\n  searchKeyword.value = value;\r\n  if (!value || value.trim() === '') {\r\n    handleClear();\r\n  }\r\n};\r\n\r\nconst handleSearch = () => {\r\n  const keyword = searchKeyword.value.trim();\r\n  queryParams.title = keyword || null;\r\n  loadArticles(true);\r\n};\r\n\r\nconst handleClear = () => {\r\n  searchKeyword.value = '';\r\n  queryParams.title = null;\r\n  loadArticles(true);\r\n  setTimeout(() => uni.hideKeyboard(), 300);\r\n};\r\n\r\nconst onImageLoad = () => {};\r\nconst onImageError = () => {};\r\n\r\nconst gotoDetail = (id) => {\r\n  uni.navigateTo({\r\n    url: `/pages_sub/pages_article/detail?id=${id}`,\r\n  });\r\n};\r\n\r\nconst formatDate = (dateString) => {\r\n  if (!dateString) return '';\r\n  return dateString.split(' ')[0];\r\n};\r\n\r\nconst loadMore = () => {\r\n  if (loadStatus.value === 'loadmore') {\r\n    queryParams.pageNum++;\r\n    loadArticles();\r\n  }\r\n};\r\n\r\nconst initPageData = async () => {\r\n  try {\r\n    await Promise.all([\r\n      loadTags(),\r\n      loadRegions(),\r\n      loadArticles(true)\r\n    ]);\r\n  } catch (error) {\r\n    console.error('页面初始化失败:', error);\r\n    uni.showToast({title: '页面加载失败', icon: 'none'});\r\n  }\r\n};\r\n\r\nonMounted(() => {\r\n  // 【新增】页面加载时，从全局缓存读取静态资源\r\n  assets.value = uni.getStorageSync('staticAssets') || {};\r\n  initPageData();\r\n});\r\n\r\nonShow(() => {\r\n  uni.hideTabBar();\r\n});\r\n\r\nonPullDownRefresh(() => {\r\n  loadArticles(true);\r\n});\r\n\r\nonReachBottom(() => {\r\n  loadMore();\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n/* 所有样式保持不变 */\r\n:root {\r\n  --radius-small: 8rpx;\r\n  --radius-medium: 16rpx;\r\n  --radius-large: 24rpx;\r\n  --radius-xl: 32rpx;\r\n  --separator-color: #E5E7EB;\r\n  --content-padding: 32rpx;\r\n}\r\n.page-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100vh;\r\n  background: #FFFFFF;\r\n}\r\n.header-section {\r\n  position: relative;\r\n  flex-shrink: 0;\r\n  z-index: 10;\r\n  /* 【修改】移除 background 属性 */\r\n  background-color: #f0f2f5; /* 添加一个基础背景色，防止图片加载慢时白屏 */\r\n  background-position: center;\r\n  background-size: cover;\r\n  background-repeat: no-repeat;\r\n  padding-bottom: 24rpx;\r\n}\r\n.custom-nav-bar {\r\n  padding: 0 var(--content-padding) 32rpx;\r\n  .status-bar {\r\n    height: var(--status-bar-height);\r\n  }\r\n  .nav-title {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 184rpx;\r\n    box-sizing: border-box;\r\n    padding-top: var(--status-bar-height);\r\n    font-size: 34rpx;\r\n    font-weight: bold;\r\n    color: #FFFFFF;\r\n  }\r\n}\r\n.filter-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 32rpx;\r\n}\r\n.sort-button,\r\n.filter-button {\r\n  font-family: \"Alibaba PuHuiTi 3.0-55 Regular\", sans-serif;\r\n  font-size: 28rpx;\r\n  color: #FFFFFF;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8rpx;\r\n  position: relative;\r\n  transition: opacity 0.2s ease;\r\n  &.is-active {\r\n    opacity: 0.8;\r\n  }\r\n}\r\n.filter-button .active-dot {\r\n  position: absolute;\r\n  top: -4rpx;\r\n  right: -16rpx;\r\n  width: 12rpx;\r\n  height: 12rpx;\r\n  background-color: #ef4444;\r\n  border-radius: 50%;\r\n  border: 2rpx solid #0F4CA7;\r\n}\r\n.search-box {\r\n  flex: 1;\r\n  height: 64rpx;\r\n  background: #FFFFFF;\r\n  border-radius: 32rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 24rpx;\r\n  margin-left: auto;\r\n  :deep(.uni-easyinput__content) {\r\n    background: transparent !important;\r\n    border: none !important;\r\n  }\r\n  :deep(.uni-easyinput__placeholder) {\r\n    color: #9B9A9A !important;\r\n  }\r\n}\r\n.dropdown-wrapper {\r\n  position: fixed;\r\n  z-index: 999;\r\n  top: 326rpx;\r\n  left: 0;\r\n  right: 0;\r\n}\r\n.dropdown-mask {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100vh;\r\n  background-color: rgba(0, 0, 0, 0.5);\r\n  transition: opacity 0.3s ease;\r\n}\r\n.dropdown-panel {\r\n  position: absolute;\r\n  top: 0;\r\n  width: 100%;\r\n  background-color: #FFFFFF;\r\n  border-radius: var(--radius-large);\r\n  box-shadow: 0 10rpx 20rpx rgba(0, 0, 0, 0.1);\r\n  opacity: 0;\r\n  transform: translateY(-20px);\r\n  pointer-events: none;\r\n  transition: transform 0.25s ease, opacity 0.25s ease;\r\n  &.show {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n    pointer-events: auto;\r\n  }\r\n}\r\n.sort-panel {\r\n  padding: 16rpx 0;\r\n  .sort-option {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 28rpx 32rpx;\r\n    font-size: 28rpx;\r\n    color: #333333;\r\n    transition: all 0.2s ease;\r\n    &.active {\r\n      color: #023F98;\r\n      font-weight: 500;\r\n      background-color: rgba(42, 97, 241, 0.05);\r\n    }\r\n  }\r\n}\r\n.filter-panel {\r\n  display: flex;\r\n  flex-direction: column;\r\n  max-height: 70vh;\r\n  .filter-scroll {\r\n    flex: 1;\r\n    padding: 32rpx;\r\n  }\r\n  .panel-section {\r\n    margin-bottom: 48rpx;\r\n    &:last-child {\r\n      margin-bottom: 24rpx;\r\n    }\r\n    .section-title {\r\n      font-weight: 500;\r\n      font-size: 30rpx;\r\n      color: #23232A;\r\n      margin-bottom: 28rpx;\r\n      display: block;\r\n    }\r\n    .panel-options {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      gap: 24rpx;\r\n    }\r\n    .option-btn {\r\n      width: 200rpx;\r\n      height: 68rpx;\r\n      background: #F2F4FA;\r\n      border-radius: var(--radius-small);\r\n      font-size: 28rpx;\r\n      color: #66666E;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      padding: 0;\r\n      transition: all 0.2s ease;\r\n      &.active {\r\n        background: rgba(42, 97, 241, 0.1);\r\n        color: #023F98;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n  }\r\n  .panel-footer {\r\n    display: flex;\r\n    gap: 24rpx;\r\n    padding: 24rpx 32rpx 32rpx;\r\n    border-top: 1rpx solid #F0F2F5;\r\n    .footer-btn {\r\n      flex: 1;\r\n      height: 80rpx;\r\n      margin: 0;\r\n      font-size: 30rpx;\r\n      border-radius: var(--radius-small);\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      border: none;\r\n      &.reset {\r\n        background-color: #F2F4FA;\r\n        color: #66666E;\r\n      }\r\n      &.confirm {\r\n        background: #0F4CA7;\r\n        color: #FFFFFF;\r\n      }\r\n    }\r\n  }\r\n}\r\n.content-section {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  min-height: 0;\r\n  background: #FFFFFF;\r\n  margin-top: -24rpx;\r\n  border-top-left-radius: var(--radius-large);\r\n  border-top-right-radius: var(--radius-large);\r\n  position: relative;\r\n  z-index: 20;\r\n}\r\n.tabs-container {\r\n  flex-shrink: 0;\r\n  width: 100%;\r\n  background: #ffffff;\r\n  border-top-left-radius: inherit;\r\n  border-top-right-radius: inherit;\r\n  position: relative;\r\n  &:after {\r\n    content: '';\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: var(--content-padding);\r\n    right: var(--content-padding);\r\n    height: 1rpx;\r\n    background-color: var(--separator-color);\r\n  }\r\n  :deep(.u-tabs) {\r\n    padding: 12rpx 0;\r\n  }\r\n}\r\n.article-list-scroll {\r\n  flex: 1;\r\n  height: 0;\r\n  box-sizing: border-box;\r\n  overflow-y: auto;\r\n  -webkit-overflow-scrolling: touch;\r\n  padding-bottom: calc(160rpx + constant(safe-area-inset-bottom));\r\n  padding-bottom: calc(160rpx + env(safe-area-inset-bottom));\r\n}\r\n.article-list {\r\n  background: #FFFFFF;\r\n}\r\n.article-card {\r\n  display: flex;\r\n  gap: 30rpx;\r\n  padding: 32rpx var(--content-padding);\r\n  background: #ffffff;\r\n  position: relative;\r\n  &:not(:last-child) {\r\n    border-bottom: 1rpx solid var(--separator-color);\r\n  }\r\n  .card-content {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    min-width: 0;\r\n  }\r\n  .card-title {\r\n    font-size: 30rpx;\r\n    font-weight: 600;\r\n    color: #1e293b;\r\n    line-height: 1.5;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    display: -webkit-box;\r\n    -webkit-line-clamp: 2;\r\n    -webkit-box-orient: vertical;\r\n    margin-bottom: auto;\r\n  }\r\n  .card-tags {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    gap: 16rpx;\r\n    margin-top: 16rpx;\r\n  }\r\n  .tag-item {\r\n    width: 112rpx;\r\n    height: 40rpx;\r\n    background: #FFFFFF;\r\n    border-radius: 4rpx;\r\n    border: 1rpx solid #023F98;\r\n    color: #023F98;\r\n    font-size: 22rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 0;\r\n  }\r\n  .card-meta {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 32rpx;\r\n    margin-top: 24rpx;\r\n    font-size: 24rpx;\r\n    color: #9B9A9A;\r\n  }\r\n  .card-cover {\r\n    flex-shrink: 0;\r\n    width: 336rpx;\r\n    height: 190rpx;\r\n    border-radius: var(--radius-medium);\r\n    overflow: hidden;\r\n  }\r\n}\r\n.image-loading,\r\n.image-error {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #f8f8f8;\r\n  border-radius: var(--radius-medium);\r\n}\r\n.loading-text,\r\n.error-text {\r\n  font-size: 24rpx;\r\n  color: #9ca3af;\r\n  margin-top: 8rpx;\r\n}\r\n:deep(.u-loadmore__content__text) {\r\n  font-size: 24rpx !important;\r\n  color: #9B9A9A !important;\r\n  line-height: 34rpx !important;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/all code/hongda-wxview/hongda-wxview/pages/article/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "computed", "uni", "getArticleList", "listAllTag", "listAllRegion", "onMounted", "onShow", "onPullDownRefresh", "onReachBottom"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAsKA,MAAM,eAAe,MAAW;;;;AAQhC,UAAM,cAAcA,cAAAA,IAAI,CAAA,CAAE;AAC1B,UAAM,UAAUA,cAAAA,IAAI,CAAC,EAAE,IAAI,MAAM,MAAM,KAAM,CAAA,CAAC;AAC9C,UAAM,kBAAkBA,cAAAA,IAAI,CAAC;AAC7B,UAAM,aAAaA,cAAAA,IAAI,UAAU;AACjC,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAC5B,UAAM,cAAcA,cAAAA,IAAI,IAAI;AAC5B,UAAM,SAASA,cAAAA,IAAI,CAAA,CAAE;AAGrB,UAAM,cAAcC,cAAAA,SAAS;AAAA,MAC3B,SAAS;AAAA,MACT,UAAU;AAAA,MACV,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,eAAe;AAAA,MACf,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,4BAA4B;AAAA,MAC5B,0BAA0B;AAAA,MAC1B,kBAAkB;AAAA,IACpB,CAAC;AAGD,UAAM,cAAcA,cAAAA,SAAS;AAAA,MAC3B,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAED,UAAM,iBAAiBA,cAAAA,SAAS;AAAA,MAC9B,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAGD,UAAM,cAAc;AAAA,MAClB,EAAE,MAAM,QAAQ,OAAO,UAAW;AAAA,MAClC,EAAE,MAAM,QAAQ,OAAO,eAAgB;AAAA,MACvC,EAAE,MAAM,QAAQ,OAAO,aAAc;AAAA,IACvC;AAEA,UAAM,gBAAgBD,cAAAA,IAAI,CAAA,CAAE;AAE5B,UAAM,cAAc;AAAA,MAClB,EAAE,MAAM,QAAQ,OAAO,MAAO;AAAA,MAC9B,EAAE,MAAM,QAAQ,OAAO,OAAQ;AAAA,MAC/B,EAAE,MAAM,QAAQ,OAAO,QAAS;AAAA,MAChC,EAAE,MAAM,QAAQ,OAAO,OAAQ;AAAA,IACjC;AAKA,UAAM,cAAcE,cAAQ,SAAC,MAAM;AAEjC,YAAM,WAAW,OAAO,MAAM;AAC9B,UAAI,UAAU;AACZ,eAAO;AAAA,UACL,iBAAiB,QAAQ,QAAQ;AAAA,QACvC;AAAA,MACG;AAED,aAAO;IACT,CAAC;AAED,UAAM,iBAAiBA,cAAQ,SAAC,MAAM;AACpC,YAAM,WAAW,YAAY,KAAK,UAAQ,KAAK,UAAU,eAAe,IAAI;AAC5E,aAAO,WAAW,SAAS,OAAO;AAAA,IACpC,CAAC;AAED,UAAM,iBAAiBA,cAAAA,SAAS,MAAM,eAAe,WAAW,SAAS,eAAe,SAAS,KAAK;AAEtG,UAAM,mBAAmBA,cAAQ,SAAC,MAAM;AACtC,UAAI,CAAC,eAAe,OAAO;AACzB,eAAO;AAAA,MACR;AACD,UAAI,QAAQ;AACZ,UAAI,eAAe,WAAW;AAAO;AACrC,UAAI,eAAe,SAAS;AAAO;AACnC,aAAO,MAAM,KAAK;AAAA,IACpB,CAAC;AAGD,UAAM,cAAc,CAAC,cAAc;AACjC,UAAI,YAAY,UAAU,WAAW;AACnC,oBAAY,QAAQ;AAAA,MACxB,OAAS;AACL,oBAAY,OAAO,eAAe;AAClC,oBAAY,SAAS,eAAe;AACpC,oBAAY,OAAO,eAAe;AAClC,oBAAY,QAAQ;AAAA,MACrB;AAAA,IACH;AAEA,UAAM,aAAa,MAAM;AACvB,kBAAY,QAAQ;AAAA,IACtB;AAEA,UAAM,mBAAmB,CAAC,SAAS;AACjC,qBAAe,OAAO,KAAK;AAC3B,kBAAY,gBAAgB,KAAK,UAAU,YAAY,eAAe,KAAK;AAC3E,kBAAY,QAAQ,KAAK,UAAU,YAAY,QAAQ;AACvD;AACA,mBAAa,IAAI;AAAA,IACnB;AAEA,UAAM,oBAAoB,MAAM;AAC9B,qBAAe,SAAS;AACxB,qBAAe,OAAO;AACtB,kBAAY,SAAS;AACrB,kBAAY,OAAO;AACnB;AACA;AACA,mBAAa,IAAI;AAAA,IACnB;AAEA,UAAM,sBAAsB,MAAM;AAChC,qBAAe,SAAS,YAAY;AACpC,qBAAe,OAAO,YAAY;AAClC;AACA;AACA,mBAAa,IAAI;AAAA,IACnB;AAEA,UAAM,qBAAqB,MAAM;AAC/B,kBAAY,gBAAgB,IAAI,eAAe,WAAW,QAAQ,OAAO,eAAe;AACxF,YAAM,MAAM,oBAAI;AAChB,UAAI,YAAY;AAChB,UAAI,eAAe,SAAS,OAAO;AACjC,cAAM,UAAU,oBAAI;AACpB,YAAI,eAAe,SAAS;AAAQ,kBAAQ,QAAQ,IAAI,YAAY,CAAC;AAAA,iBAC5D,eAAe,SAAS;AAAS,kBAAQ,SAAS,IAAI,aAAa,CAAC;AAAA,iBACpE,eAAe,SAAS;AAAQ,kBAAQ,YAAY,IAAI,gBAAgB,CAAC;AAClF,oBAAY,GAAG,QAAQ,aAAa,IAAI,OAAO,QAAQ,aAAa,CAAC,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,OAAO,QAAQ,SAAS,EAAE,SAAS,GAAG,GAAG,CAAC;AAAA,MACtI;AACD,kBAAY,0BAA0B,IAAI;AAAA,IAC5C;AAEA,UAAM,gBAAgB,CAAC,SAAS;AAC9B,aAAO,KAAK,IAAI,CAAC,YAAY;AAC3B,YAAI,aAAa,CAAA;AACjB,YAAI,QAAQ,QAAQ,OAAO,QAAQ,SAAS,UAAU;AACpD,cAAI;AACF,yBAAa,KAAK,MAAM,QAAQ,IAAI;AAAA,UACrC,SAAQ,GAAG;AACVC,0BAAAA,MAAA,MAAA,SAAA,kCAAc,iBAAiB,QAAQ,IAAI,QAAQ,MAAM,CAAC;AAAA,UAC3D;AAAA,QACF;AACD,eAAO,EAAE,GAAG,SAAS,YAAY,MAAM,QAAQ,UAAU,IAAI,aAAa,CAAA;MAC9E,CAAG;AAAA,IACH;AAEA,UAAM,eAAe,OAAO,YAAY,UAAU;AAChD,UAAI,CAAC,aAAa,WAAW,UAAU;AAAU;AACjD,UAAI,WAAW;AACb,oBAAY,UAAU;AACtB,oBAAY,QAAQ;MACrB;AACD,iBAAW,QAAQ;AACnB,UAAI;AACF,cAAM,WAAW,MAAMC,mCAAe,WAAW;AACjD,cAAM,cAAc,cAAc,SAAS,IAAI;AAC/C,YAAI,WAAW;AACb,sBAAY,QAAQ;AAAA,QAC1B,OAAW;AACL,sBAAY,MAAM,KAAK,GAAG,WAAW;AAAA,QACtC;AACD,YAAI,SAAS,KAAK,SAAS,YAAY,YAAY,YAAY,MAAM,UAAU,SAAS,OAAO;AAC7F,qBAAW,QAAQ;AAAA,QACzB,OAAW;AACL,qBAAW,QAAQ;AAAA,QACpB;AAAA,MACF,SAAQ,OAAO;AACd,mBAAW,QAAQ;AACnBD,sBAAc,MAAA,MAAA,SAAA,kCAAA,aAAa,KAAK;AAChCA,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAM,CAAE;AAAA,MACjD,UAAY;AACRA,sBAAG,MAAC,oBAAmB;AAAA,MACxB;AAAA,IACH;AAEA,UAAM,WAAW,YAAY;AAC3B,UAAI;AACF,cAAM,WAAW,MAAME,gBAAAA;AACvB,cAAM,cAAc,MAAM,QAAQ,SAAS,IAAI,IAAI,SAAS,OAAO;AACnE,gBAAQ,QAAQ,CAAC,EAAE,IAAI,MAAM,MAAM,KAAI,GAAI,GAAG,WAAW;AAAA,MAC1D,SAAQ,OAAO;AACdF,sBAAc,MAAA,MAAA,SAAA,kCAAA,aAAa,KAAK;AAAA,MACjC;AAAA,IACH;AAEA,UAAM,cAAc,YAAY;AAC9B,UAAI;AACF,cAAM,WAAW,MAAMG,mBAAAA;AACvB,cAAM,mBAAmB,SAAS,KAAK,IAAI,WAAS;AAAA,UAClD,MAAM,KAAK;AAAA,UACX,OAAO,KAAK;AAAA,QACb,EAAC;AACF,sBAAc,QAAQ,CAAC,EAAE,MAAM,QAAQ,OAAO,MAAK,GAAI,GAAG,gBAAgB;AAAA,MAC3E,SAAQ,OAAO;AACdH,sBAAc,MAAA,MAAA,SAAA,kCAAA,aAAa,KAAK;AAChC,sBAAc,QAAQ,CAAC,EAAE,MAAM,QAAQ,OAAO,MAAK,CAAE;AAAA,MACtD;AAAA,IACH;AAEA,UAAM,kBAAkB,CAAC,QAAQ;AAC/B,sBAAgB,QAAQ,IAAI;AAC5B,kBAAY,SAAS,IAAI,OAAO,OAAO,OAAO,OAAO,IAAI,EAAE;AAC3D,mBAAa,IAAI;AAAA,IACnB;AAEA,UAAM,gBAAgB,CAAC,UAAU;AAC/B,oBAAc,QAAQ;AACtB,UAAI,CAAC,SAAS,MAAM,KAAI,MAAO,IAAI;AACjC;MACD;AAAA,IACH;AAEA,UAAM,eAAe,MAAM;AACzB,YAAM,UAAU,cAAc,MAAM,KAAI;AACxC,kBAAY,QAAQ,WAAW;AAC/B,mBAAa,IAAI;AAAA,IACnB;AAEA,UAAM,cAAc,MAAM;AACxB,oBAAc,QAAQ;AACtB,kBAAY,QAAQ;AACpB,mBAAa,IAAI;AACjB,iBAAW,MAAMA,cAAG,MAAC,aAAc,GAAE,GAAG;AAAA,IAC1C;AAEA,UAAM,cAAc,MAAM;AAAA,IAAA;AAC1B,UAAM,eAAe,MAAM;AAAA,IAAA;AAE3B,UAAM,aAAa,CAAC,OAAO;AACzBA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,sCAAsC,EAAE;AAAA,MACjD,CAAG;AAAA,IACH;AAEA,UAAM,aAAa,CAAC,eAAe;AACjC,UAAI,CAAC;AAAY,eAAO;AACxB,aAAO,WAAW,MAAM,GAAG,EAAE,CAAC;AAAA,IAChC;AAEA,UAAM,WAAW,MAAM;AACrB,UAAI,WAAW,UAAU,YAAY;AACnC,oBAAY;AACZ;MACD;AAAA,IACH;AAEA,UAAM,eAAe,YAAY;AAC/B,UAAI;AACF,cAAM,QAAQ,IAAI;AAAA,UAChB,SAAU;AAAA,UACV,YAAa;AAAA,UACb,aAAa,IAAI;AAAA,QACvB,CAAK;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,kCAAc,YAAY,KAAK;AAC/BA,sBAAG,MAAC,UAAU,EAAC,OAAO,UAAU,MAAM,OAAM,CAAC;AAAA,MAC9C;AAAA,IACH;AAEAI,kBAAAA,UAAU,MAAM;AAEd,aAAO,QAAQJ,cAAG,MAAC,eAAe,cAAc,KAAK,CAAA;AACrD;IACF,CAAC;AAEDK,kBAAAA,OAAO,MAAM;AACXL,oBAAG,MAAC,WAAU;AAAA,IAChB,CAAC;AAEDM,kBAAAA,kBAAkB,MAAM;AACtB,mBAAa,IAAI;AAAA,IACnB,CAAC;AAEDC,kBAAAA,cAAc,MAAM;AAClB;IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvcD,GAAG,WAAW,eAAe;"}