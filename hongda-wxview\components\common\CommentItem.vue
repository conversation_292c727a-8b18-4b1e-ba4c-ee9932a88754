<template>
  <view class="comment-item">
    <view class="top-comment">
      <view class="comment-avatar">
        <image :src="comment.avatarUrl || defaultAvatarUrl" class="avatar-img"></image>
      </view>
      <view class="comment-content">
        <view class="comment-header">
          <text class="comment-author">{{ comment.nickname || '匿名用户' }}</text>
          <text class="comment-time">{{ formatDateTime(comment.createTime) }}</text>
        </view>
        <text class="comment-text">{{ comment.content }}</text>
          <view class="comment-actions">
            <view class="action-item" @click.stop="handleReplyClick(comment)">
            <text class="iconfont icon-message-circle"></text>
            <text>回复</text>
          </view>
        </view>
      </view>
    </view>

    <view class="replies-container" v-if="flatReplies.length > 0">
      <view class="reply-list">
        <view
            v-for="reply in displayedReplies"
            :key="reply.id"
            class="reply-item"
        >
          <view class="reply-avatar">
            <image :src="reply.avatarUrl || defaultAvatarUrl" class="reply-avatar-img"></image>
          </view>

          <view class="reply-content">
            <view class="reply-header">
              <text class="reply-author">{{ reply.nickname || '匿名用户' }}</text>
              <view class="reply-to" v-if="reply.replyToNickname">
                <text class="reply-text">回复</text>
                <text class="reply-target">@{{ reply.replyToNickname }}</text>
              </view>
            </view>

            <text class="reply-text-content">{{ reply.content }}</text>

            <view class="reply-footer">
              <text class="reply-time">{{ formatDateTime(reply.createTime) }}</text>
              <view class="reply-actions">
                <view class="action-item" @click.stop="handleReplyClick(reply)">
                  <text class="iconfont icon-message-circle"></text>
                  <text>回复</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="expand-toggle" v-if="flatReplies.length > maxDisplayReplies" @click="toggleExpand">
        <text class="expand-text">
          {{ isExpanded ? `收起回复` : `共${flatReplies.length}条回复 >` }}
        </text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  comment: {
    type: Object,
    required: true
  },
  maxDisplayReplies: {
    type: Number,
    default: 3
  }
});

const emit = defineEmits(['reply']);
const assets = ref(uni.getStorageSync('staticAssets') || {});

// 2. 创建一个计算属性，用于获取默认头像的URL
const defaultAvatarUrl = computed(() => {
  // 使用我们约定的“暗号” default_avatar
  return assets.value.default_avatar || '';
});

/**
 * 【新增】日期时间格式化函数
 * @param {string | Date} time - 需要格式化的时间
 * @returns {string} 格式化后的字符串，例如 "2025-07-21 10:32:21"
 */
const formatDateTime = (time) => {
  if (!time) return '';
  const date = new Date(time);

  const Y = date.getFullYear();
  const M = (date.getMonth() + 1).toString().padStart(2, '0');
  const D = date.getDate().toString().padStart(2, '0');

  const h = date.getHours().toString().padStart(2, '0');
  const m = date.getMinutes().toString().padStart(2, '0');
  const s = date.getSeconds().toString().padStart(2, '0');

  return `${Y}-${M}-${D} ${h}:${m}:${s}`;
};


// 展开状态
const isExpanded = ref(false);

// 将树形回复数据扁平化的核心函数
const flattenReplies = (children) => {
  const result = [];
  const traverse = (nodes) => {
    if (!nodes || !Array.isArray(nodes)) return;
    nodes.forEach(node => {
      result.push({
        ...node,
        replyToNickname: node.replyToNickname || null
      });
      if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    });
  };
  traverse(children);
  return result;
};

// 扁平化的回复列表
const flatReplies = computed(() => {
  if (!props.comment.children || !Array.isArray(props.comment.children)) {
    return [];
  }
  return flattenReplies(props.comment.children);
});

// 根据展开状态决定显示的回复列表
const displayedReplies = computed(() => {
  if (isExpanded.value || flatReplies.value.length <= props.maxDisplayReplies) {
    return flatReplies.value;
  }
  return flatReplies.value.slice(0, props.maxDisplayReplies);
});

// 切换展开状态
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

// 跳转登录前记录当前页面，供登录后返回
const getCurrentPageUrl = () => {
  try {
    const pages = getCurrentPages();
    const current = pages[pages.length - 1];
    const route = '/' + current.route;
    const options = current.options || {};
    const query = Object.keys(options)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(options[key])}`)
      .join('&');
    return query ? `${route}?${query}` : route;
  } catch (e) {
    return '';
  }
};

// 统一的登录校验
const ensureLoggedIn = () => {
  try {
    const token = uni.getStorageSync('token');
    if (!token) {
      const backUrl = getCurrentPageUrl();
      try { if (backUrl) uni.setStorageSync('loginBackPage', backUrl); } catch (e) {}
      uni.navigateTo({ url: '/pages_sub/pages_other/login' });
      return false;
    }
    return true;
  } catch (e) {
    uni.navigateTo({ url: '/pages_sub/pages_other/login' });
    return false;
  }
};

// 点击回复时先校验登录
const handleReplyClick = (target) => {
  if (!ensureLoggedIn()) return;
  emit('reply', target);
};
</script>

<style lang="scss" scoped>
/* --- 样式部分无需修改，但为了完整性一并提供 --- */
.comment-item {
  display: flex;
  flex-direction: column;
  background: #fff;
  margin-bottom: 12px;
}

/* 顶级评论样式 */
.top-comment {
  display: flex;
  padding: 16px;
  gap: 12px;
}

.comment-avatar {
  flex-shrink: 0;

  .avatar-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }
}

.comment-content {
  flex: 1;
  min-width: 0;
}

.comment-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;

  .comment-author {
    font-size: 15px;
    color: #333;
    font-weight: 500;
  }

  .comment-time {
    font-size: 13px;
    color: #999;
  }
}

.comment-text {
  display: block;
  font-size: 16px;
  color: #333;
  line-height: 1.6;
  margin-bottom: 12px;
  word-break: break-all;
  white-space: pre-wrap;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 24px;

  .action-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    color: #999;
    cursor: pointer;

    .iconfont {
      font-size: 16px;
    }

    &:active {
      opacity: 0.7;
    }
  }
}

/* 回复容器样式 */
.replies-container {
  margin: 0 10px 0 52px; /* 左侧对齐头像右边缘 */
  background: #f7f8fa;
  border-radius: 8px;
  overflow: hidden;
}

.reply-list {
  padding: 4px 0;
}

.reply-item {
  display: flex;
  padding: 12px 16px;
  gap: 10px;
  border-bottom: 1px solid #eee;

  &:last-child {
    border-bottom: none;
  }
}

.reply-avatar {
  flex-shrink: 0;

  .reply-avatar-img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
  }
}

.reply-content {
  width: 100%;
}

.reply-header {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 8px;

  .reply-author {
    font-size: 14px;
    color: #576b95;
    font-weight: 500;
  }

  .reply-to {
    display: flex;
    align-items: center;
    gap: 4px;
    background: #e8f4fd;
    padding: 2px 8px;
    border-radius: 12px;

    .reply-text {
      font-size: 12px;
      color: #576b95;
    }

    .reply-target {
      font-size: 12px;
      color: #007bff;
      font-weight: 500;
    }
  }
}

.reply-text-content {
  display: block;
  font-size: 15px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8px;
  word-break: break-all;
  white-space: pre-wrap;
}

.reply-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .reply-time {
    font-size: 12px;
    color: #999;
  }

  .reply-actions {
    display: flex;
    align-items: center;
    gap: 16px;

    .action-item {
      display: flex;
      align-items: center;
      gap: 3px;
      font-size: 13px;
      color: #999;
      cursor: pointer;

      .iconfont {
        font-size: 14px;
      }

      &:active {
        opacity: 0.7;
      }
    }
  }
}

/* 展开/收起按钮 */
.expand-toggle {
  padding: 12px 16px;
  text-align: center;
  border-top: 1px solid #eee;
  background: #fafbfc;
  cursor: pointer;

  .expand-text {
    font-size: 14px;
    color: #576b95;
  }

  &:active {
    background: #f0f1f3;
  }
}
</style>