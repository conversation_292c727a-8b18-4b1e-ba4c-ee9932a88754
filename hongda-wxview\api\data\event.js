/**
 * 活动相关API接口
 */
import http from '@/utils/request.js';
import { API_PATHS } from '@/utils/config.js';

/**
 * 获取活动列表
 * @param {Object} params 查询参数
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页数量
 * @param {string} params.title 活动标题（搜索）
 * @param {string} params.location 活动地点
 * @param {number} params.status 活动状态
 * @param {string} params.startTime 开始时间
 * @param {string} params.endTime 结束时间
 * @param {string} params.orderBy 排序字段
 * @param {string} params.isAsc 是否升序
 * @param {number} params.isHot 是否热门 (1: 热门, 0: 非热门)
 * @returns {Promise} API响应
 */
export const getEventListApi = (params) => {
  return http.get(API_PATHS.EVENT_LIST, params);
};

/**
 * 获取活动详情
 * @param {number} id 活动ID
 * @returns {Promise} API响应
 */
export const getEventDetailApi = (id) => {
  return http.get(`${API_PATHS.EVENT_DETAIL}/${id}`);
};

/**
 * 获取热门活动列表（首页用）
 * @param {number} limit 限制数量，默认5条
 * @returns {Promise} API响应
 */
export const getHotEventListApi = (limit = 5) => {
  return http.get(API_PATHS.EVENT_LIST, { 
    pageNum: 1, 
    pageSize: limit, 
    isHot: 1,
    status: 1 // 只获取报名中的热门活动
  });
};

/**
 * 获取活动地区列表（用于筛选下拉框）
 * @returns {Promise} API响应
 */
export const getEventLocationsApi = () => {
  return http.get(API_PATHS.EVENT_LOCATIONS);
};

/**
 * 获取活动城市列表（用于地区筛选）
 * @returns {Promise} API响应
 */
export const getEventCitiesApi = () => {
  return http.get('/events/cities');
};

/**
 * 获取即将开始的活动列表
 * @param {number} limit 限制数量，默认10条
 * @returns {Promise} API响应
 */
export const getUpcomingEventsApi = (limit = 10) => {
  const now = new Date();
  const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
  
  return http.get(API_PATHS.EVENT_LIST, {
    pageNum: 1,
    pageSize: limit,
    status: 1, // 报名中
    startTime: now.toISOString(),
    endTime: nextWeek.toISOString(),
    orderBy: 'startTime',
    isAsc: 'asc'
  });
};

/**
 * 按地区获取活动列表
 * @param {string} location 地区名称
 * @param {Object} options 其他选项
 * @returns {Promise} API响应
 */
export const getEventsByLocationApi = (location, options = {}) => {
  const params = {
    pageNum: 1,
    pageSize: 20,
    location,
    status: 1, // 默认只获取报名中的活动
    ...options
  };
  
  return http.get(API_PATHS.EVENT_LIST, params);
};

/**
 * 搜索活动
 * @param {Object} params 查询参数, e.g., { pageNum, pageSize, title, ... }
 * @returns {Promise} API响应
 */
export const searchEventsApi = (params) => {
  // 直接将页面传递过来的参数对象透传给API请求
  return http.get(API_PATHS.EVENT_LIST, params);
};

/**
 * 获取日历视图活动列表
 * 专门为日历视图优化：只返回状态0/1的活动，按开始时间从近到远排序
 * @param {Object} params 查询参数
 * @param {number} params.pageNum 页码
 * @param {number} params.pageSize 每页数量
 * @param {string} params.title 活动标题（搜索）
 * @param {string} params.location 活动地点
 * @param {string} params.timeRangeStart 时间范围开始
 * @param {string} params.timeRangeEnd 时间范围结束
 * @returns {Promise} API响应
 */
export const getCalendarEventsApi = (params) => {
  return http.get('/events/calendar', params);
};

/**
 * 导出活动列表
 * @param {Object} params 查询参数
 * @returns {Promise} API响应
 */
export const exportEventListApi = (params) => {
  return http.post(API_PATHS.EVENT_EXPORT, params, {
    responseType: 'blob'
  });
}; 