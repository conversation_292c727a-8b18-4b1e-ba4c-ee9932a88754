"use strict";
const common_vendor = require("../../common/vendor.js");
const api_content_country = require("../../api/content/country.js");
const utils_config = require("../../utils/config.js");
if (!Array) {
  const _easycom_uni_search_bar2 = common_vendor.resolveComponent("uni-search-bar");
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  (_easycom_uni_search_bar2 + _easycom_uni_load_more2)();
}
const _easycom_uni_search_bar = () => "../../uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar.js";
const _easycom_uni_load_more = () => "../../uni_modules/uni-load-more/components/uni-load-more/uni-load-more.js";
if (!Math) {
  (_easycom_uni_search_bar + _easycom_uni_load_more + CustomTabBar)();
}
const CustomTabBar = () => "../../components/layout/CustomTabBar.js";
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const headerBgUrl = common_vendor.computed(() => assets.value.bg_country_list_header || "");
    const activeTabBgUrl = common_vendor.computed(() => assets.value.bg_country_list_active_tab || "");
    const baseUrl = utils_config.IMAGE_BASE_URL;
    const searchKeyword = common_vendor.ref("");
    const activeContinent = common_vendor.ref("ALL");
    const countryList = common_vendor.ref([]);
    const loading = common_vendor.ref(false);
    const instance = common_vendor.getCurrentInstance();
    const assets = common_vendor.ref(common_vendor.index.getStorageSync("staticAssets") || {});
    const continents = [
      { label: "全部", value: "ALL" },
      { label: "亚洲", value: "ASIA" },
      { label: "欧洲", value: "EUROPE" },
      { label: "北美洲", value: "NORTH_AMERICA" },
      { label: "南美洲", value: "SOUTH_AMERICA" },
      { label: "非洲", value: "AFRICA" },
      { label: "大洋洲", value: "OCEANIA" }
    ];
    const activeIndex = common_vendor.ref(0);
    const tabInitialPositions = common_vendor.ref([]);
    const tabsScrollLeft = common_vendor.ref(0);
    const indicatorLeft = common_vendor.computed(() => {
      if (!tabInitialPositions.value || tabInitialPositions.value.length === 0) {
        return -999;
      }
      const activeTabInfo = tabInitialPositions.value[activeIndex.value];
      if (!activeTabInfo) {
        return -999;
      }
      const indicatorHalfWidth = common_vendor.index.upx2px(20);
      const finalLeft = activeTabInfo.left - tabsScrollLeft.value + activeTabInfo.width / 2 - indicatorHalfWidth;
      return finalLeft;
    });
    const onTabsScroll = (event) => {
      tabsScrollLeft.value = event.detail.scrollLeft;
    };
    const calculateAllTabsPosition = () => {
      common_vendor.nextTick$1(() => {
        const query = common_vendor.index.createSelectorQuery().in(instance);
        query.selectAll(".tab-item").boundingClientRect((data) => {
          if (data && data.length) {
            tabInitialPositions.value = data;
          }
        }).exec();
      });
    };
    const fetchData = async (isRefresh = false) => {
      if (loading.value)
        return;
      loading.value = true;
      try {
        const res = await api_content_country.getCountryList({
          continent: activeContinent.value,
          keyword: searchKeyword.value
        });
        countryList.value = isRefresh ? res.data : [...countryList.value, ...res.data];
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/country/index.vue:170", "获取国别列表失败:", error);
        common_vendor.index.showToast({ title: "数据加载失败", icon: "none" });
      } finally {
        loading.value = false;
        if (isRefresh) {
          common_vendor.index.stopPullDownRefresh();
        }
      }
    };
    const onFilterTap = (continentValue, index) => {
      if (activeContinent.value === continentValue)
        return;
      activeContinent.value = continentValue;
      activeIndex.value = index;
      fetchData(true);
    };
    const onSearch = () => {
      fetchData(true);
    };
    const onCancelSearch = () => {
      searchKeyword.value = "";
      fetchData(true);
    };
    const goToDetail = (countryId) => {
      common_vendor.index.navigateTo({ url: `/pages_sub/pages_country/detail?id=${countryId}` });
    };
    const loadMore = () => {
    };
    common_vendor.onLoad(() => {
      fetchData(true);
      setTimeout(() => {
        calculateAllTabsPosition();
      }, 150);
    });
    common_vendor.onShow(() => {
      common_vendor.index.hideTabBar();
    });
    common_vendor.onPullDownRefresh(() => {
      fetchData(true);
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(onSearch),
        b: common_vendor.o(onCancelSearch),
        c: common_vendor.o(onSearch),
        d: common_vendor.o(($event) => searchKeyword.value = $event),
        e: common_vendor.p({
          placeholder: "搜索国别名称",
          radius: "100",
          bgColor: "#ffffff",
          modelValue: searchKeyword.value
        }),
        f: common_vendor.f(continents, (tab, index, i0) => {
          return {
            a: common_vendor.t(tab.label),
            b: tab.value,
            c: common_vendor.n({
              active: activeContinent.value === tab.value
            }),
            d: common_vendor.o(($event) => onFilterTap(tab.value, index), tab.value),
            e: "tab-" + index,
            f: activeContinent.value === tab.value ? `url(${activeTabBgUrl.value})` : "none"
          };
        }),
        g: common_vendor.o(onTabsScroll),
        h: indicatorLeft.value + "px",
        i: `url(${headerBgUrl.value})`,
        j: common_vendor.f(countryList.value, (item, k0, i0) => {
          return {
            a: common_vendor.unref(baseUrl) + item.listCoverUrl,
            b: common_vendor.t(item.nameCn),
            c: common_vendor.t(item.nameEn),
            d: common_vendor.t(item.summary),
            e: common_vendor.unref(baseUrl) + item.flagUrl,
            f: item.id,
            g: common_vendor.o(($event) => goToDetail(item.id), item.id)
          };
        }),
        k: loading.value
      }, loading.value ? {
        l: common_vendor.p({
          status: "loading"
        })
      } : countryList.value.length === 0 ? {} : {}, {
        m: countryList.value.length === 0,
        n: common_vendor.o(loadMore),
        o: common_vendor.p({
          current: 3
        })
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-1e6eaa19"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/country/index.js.map
