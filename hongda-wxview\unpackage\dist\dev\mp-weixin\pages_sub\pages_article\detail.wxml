<view wx:if="{{a}}" class="page-container data-v-ebaf798e"><view class="fixed-header data-v-ebaf798e" style="{{'height:' + g}}"><view class="status-bar data-v-ebaf798e" style="{{'height:' + b}}"></view><view class="custom-nav-bar data-v-ebaf798e" style="{{'height:' + e + ';' + ('padding-bottom:' + f)}}"><view class="nav-back-button data-v-ebaf798e" bindtap="{{d}}"><u-icon wx:if="{{c}}" class="data-v-ebaf798e" u-i="ebaf798e-0" bind:__l="__l" u-p="{{c}}"></u-icon></view><view class="nav-title data-v-ebaf798e">资讯详情</view></view></view><scroll-view scroll-y class="scrollable-content data-v-ebaf798e" style="{{'padding-top:' + y}}"><view class="hero-section data-v-ebaf798e"><image class="hero-image data-v-ebaf798e" src="{{h}}" mode="aspectFill"></image></view><view class="main-content data-v-ebaf798e"><view class="article-title data-v-ebaf798e">{{i}}</view><view class="article-meta-new data-v-ebaf798e"><view class="meta-left data-v-ebaf798e"><text class="meta-text data-v-ebaf798e">{{j}}</text><text class="meta-text data-v-ebaf798e">{{k}}</text></view><view class="meta-right data-v-ebaf798e"><text class="meta-text data-v-ebaf798e">{{l}} 次阅读</text></view></view><view wx:if="{{m}}" class="summary-card data-v-ebaf798e" style="{{o}}"><text class="summary-text data-v-ebaf798e">{{n}}</text></view><view class="content-card data-v-ebaf798e"><view class="content-body data-v-ebaf798e"><mp-html wx:if="{{p}}" class="data-v-ebaf798e" u-i="ebaf798e-1" bind:__l="__l" u-p="{{p}}"/></view></view></view><view wx:if="{{q}}" class="tags-section-new data-v-ebaf798e"><text class="tags-label data-v-ebaf798e">分类：</text><text wx:for="{{r}}" wx:for-item="tag" wx:key="c" class="tag-item-new data-v-ebaf798e">{{tag.a}}{{tag.b}}</text></view><view class="comment-container data-v-ebaf798e"><view class="comment-header-section data-v-ebaf798e"><text class="comment-main-title data-v-ebaf798e">留言 ({{s}})</text></view><view class="comment-input-card data-v-ebaf798e" bindtap="{{v}}"><image class="comment-input-icon data-v-ebaf798e" src="{{t}}"></image><text class="comment-input-placeholder data-v-ebaf798e">写留言</text></view><view wx:if="{{w}}" class="comment-list-container data-v-ebaf798e"><comment-item wx:for="{{x}}" wx:for-item="comment" wx:key="a" class="data-v-ebaf798e" bindreply="{{comment.b}}" u-i="{{comment.c}}" bind:__l="__l" u-p="{{comment.d}}"/></view><view wx:else class="empty-state data-v-ebaf798e"><view class="empty-icon data-v-ebaf798e">💬</view><text class="empty-title data-v-ebaf798e">暂无评论</text><text class="empty-desc data-v-ebaf798e">成为第一个发表看法的人吧</text></view></view></scroll-view><u-popup wx:if="{{A}}" class="data-v-ebaf798e" bindclose="{{z}}" u-i="ebaf798e-3" bind:__l="__l" u-p="{{A}}"></u-popup><reply-panel wx:if="{{E}}" class="data-v-ebaf798e" bindsubmit="{{B}}" bindclose="{{C}}" u-i="ebaf798e-4" bind:__l="__l" bindupdateShow="{{D}}" u-p="{{E}}"/></view><view wx:elif="{{F}}" class="error-state data-v-ebaf798e"><view class="empty-icon data-v-ebaf798e">⚠️</view><text class="empty-title data-v-ebaf798e">加载失败</text><text class="empty-desc data-v-ebaf798e">无法获取文章内容，请稍后重试</text><button class="retry-btn data-v-ebaf798e" bindtap="{{G}}">重新加载</button></view><u-popup wx:if="{{Q}}" class="data-v-ebaf798e" u-s="{{['d']}}" bindclose="{{P}}" u-i="ebaf798e-5" bind:__l="__l" u-p="{{Q}}"><view class="reply-popup data-v-ebaf798e"><view class="popup-header data-v-ebaf798e"><text class="popup-title data-v-ebaf798e">发表您的看法</text><text class="popup-close data-v-ebaf798e" bindtap="{{H}}">×</text></view><view class="popup-body data-v-ebaf798e"><block wx:if="{{r0}}"><textarea class="reply-input data-v-ebaf798e" placeholder="分享你的想法..." auto-height="{{true}}" maxlength="300" value="{{I}}" bindinput="{{J}}"></textarea></block><view class="popup-footer data-v-ebaf798e"><text class="reply-counter data-v-ebaf798e">{{K}}/300</text><button class="{{['reply-submit', 'data-v-ebaf798e', M && 'reply-submit-active']}}" disabled="{{N}}" bindtap="{{O}}">{{L}}</button></view></view></view></u-popup>