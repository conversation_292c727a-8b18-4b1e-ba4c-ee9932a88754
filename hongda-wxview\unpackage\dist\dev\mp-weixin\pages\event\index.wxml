<view class="event-list-page data-v-8e954d49"><view class="header-wrapper data-v-8e954d49"><image class="header-bg data-v-8e954d49" src="{{a}}" mode="aspectFill"></image><view class="custom-navbar data-v-8e954d49"><view class="navbar-left data-v-8e954d49" bindtap="{{c}}"><up-icon wx:if="{{b}}" class="data-v-8e954d49" u-i="8e954d49-0" bind:__l="__l" u-p="{{b}}"></up-icon></view><view class="navbar-title data-v-8e954d49"><text class="title-text data-v-8e954d49">热门活动列表</text></view><view class="navbar-right data-v-8e954d49"></view></view><view class="top-controls data-v-8e954d49"><up-subsection wx:if="{{e}}" class="data-v-8e954d49" bindchange="{{d}}" u-i="8e954d49-1" bind:__l="__l" u-p="{{e}}"></up-subsection><view class="search-wrapper data-v-8e954d49"><custom-search-box wx:if="{{i}}" class="data-v-8e954d49" bindsearch="{{f}}" bindinput="{{g}}" u-i="8e954d49-2" bind:__l="__l" bindupdateModelValue="{{h}}" u-p="{{i}}"></custom-search-box></view></view></view><view wx:if="{{j}}" class="filter-bar sticky-filter-bar data-v-8e954d49"><view class="filter-main-buttons data-v-8e954d49"><view class="filter-button data-v-8e954d49" bindtap="{{n}}"><text class="filter-text data-v-8e954d49">{{k}}</text><up-icon wx:if="{{m}}" class="{{['data-v-8e954d49', l && 'rotate-180']}}" u-i="8e954d49-3" bind:__l="__l" u-p="{{m}}"></up-icon></view><view class="filter-button data-v-8e954d49" bindtap="{{r}}"><text class="filter-text data-v-8e954d49">{{o}}</text><up-icon wx:if="{{q}}" class="{{['data-v-8e954d49', p && 'rotate-180']}}" u-i="8e954d49-4" bind:__l="__l" u-p="{{q}}"></up-icon></view><view class="filter-button data-v-8e954d49" bindtap="{{w}}"><text class="filter-text data-v-8e954d49">{{s}}</text><up-icon wx:if="{{v}}" class="{{['data-v-8e954d49', t && 'rotate-180']}}" u-i="8e954d49-5" bind:__l="__l" u-p="{{v}}"></up-icon></view><view class="filter-button data-v-8e954d49" bindtap="{{A}}"><text class="filter-text data-v-8e954d49">{{x}}</text><up-icon wx:if="{{z}}" class="{{['data-v-8e954d49', y && 'rotate-180']}}" u-i="8e954d49-6" bind:__l="__l" u-p="{{z}}"></up-icon></view></view><view wx:if="{{B}}" class="filter-panel data-v-8e954d49"><text class="section-title data-v-8e954d49">排序</text><view class="option-grid data-v-8e954d49"><view wx:for="{{C}}" wx:for-item="option" wx:key="b" class="{{['data-v-8e954d49', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-8e954d49">{{option.a}}</text></view></view><view class="filter-buttons data-v-8e954d49"><view class="filter-btn reset-btn data-v-8e954d49" bindtap="{{D}}"><text class="btn-text data-v-8e954d49">重置</text></view><view class="filter-btn complete-btn data-v-8e954d49" bindtap="{{E}}"><text class="btn-text data-v-8e954d49">完成</text></view></view></view><view wx:if="{{F}}" class="filter-panel data-v-8e954d49"><view class="option-grid data-v-8e954d49" style="margin-bottom:20rpx"><view class="{{['data-v-8e954d49', 'option-item', H]}}" bindtap="{{I}}"><text class="option-text data-v-8e954d49">{{G}}</text></view></view><text class="section-title data-v-8e954d49">热门地区</text><view class="option-grid data-v-8e954d49"><view wx:for="{{J}}" wx:for-item="option" wx:key="b" class="{{['data-v-8e954d49', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-8e954d49">{{option.a}}</text></view></view><view wx:if="{{K}}" class="data-v-8e954d49"><text class="section-title data-v-8e954d49">其他地区</text><view class="option-grid data-v-8e954d49"><view wx:for="{{L}}" wx:for-item="city" wx:key="b" class="{{['data-v-8e954d49', 'option-item', city.c]}}" bindtap="{{city.d}}"><text class="option-text data-v-8e954d49">{{city.a}}</text></view></view></view><view class="filter-buttons data-v-8e954d49"><view class="filter-btn reset-btn data-v-8e954d49" bindtap="{{M}}"><text class="btn-text data-v-8e954d49">重置</text></view><view class="filter-btn complete-btn data-v-8e954d49" bindtap="{{N}}"><text class="btn-text data-v-8e954d49">完成</text></view></view></view><view wx:if="{{O}}" class="filter-panel data-v-8e954d49"><text class="section-title data-v-8e954d49">时间</text><view class="option-grid data-v-8e954d49"><view wx:for="{{P}}" wx:for-item="option" wx:key="b" class="{{['data-v-8e954d49', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-8e954d49">{{option.a}}</text></view></view><view class="filter-buttons data-v-8e954d49"><view class="filter-btn reset-btn data-v-8e954d49" bindtap="{{Q}}"><text class="btn-text data-v-8e954d49">重置</text></view><view class="filter-btn complete-btn data-v-8e954d49" bindtap="{{R}}"><text class="btn-text data-v-8e954d49">完成</text></view></view></view><view wx:if="{{S}}" class="filter-panel data-v-8e954d49"><text class="section-title data-v-8e954d49">全部状态</text><view class="option-grid data-v-8e954d49"><view wx:for="{{T}}" wx:for-item="option" wx:key="b" class="{{['data-v-8e954d49', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-8e954d49">{{option.a}}</text></view></view><view class="filter-buttons data-v-8e954d49"><view class="filter-btn reset-btn data-v-8e954d49" bindtap="{{U}}"><text class="btn-text data-v-8e954d49">重置</text></view><view class="filter-btn complete-btn data-v-8e954d49" bindtap="{{V}}"><text class="btn-text data-v-8e954d49">完成</text></view></view></view></view><scroll-view wx:if="{{W}}" scroll-y class="event-list-scroll list-scroll-with-filter data-v-8e954d49" bindscrolltolower="{{ae}}" refresher-enabled refresher-triggered="{{af}}" bindrefresherrefresh="{{ag}}"><view wx:if="{{X}}" class="empty-state data-v-8e954d49"><up-empty wx:if="{{Y}}" class="data-v-8e954d49" u-i="8e954d49-7" bind:__l="__l" u-p="{{Y}}"></up-empty><view wx:if="{{Z}}" class="retry-container data-v-8e954d49"><up-button wx:if="{{ab}}" class="data-v-8e954d49" u-s="{{['d']}}" bindclick="{{aa}}" u-i="8e954d49-8" bind:__l="__l" u-p="{{ab}}"> 重新加载 </up-button></view></view><event-card wx:for="{{ac}}" wx:for-item="event" wx:key="a" class="data-v-8e954d49" bindclick="{{event.b}}" u-i="{{event.c}}" bind:__l="__l" u-p="{{event.d}}"/><view class="loadmore-wrapper data-v-8e954d49"><up-loadmore wx:if="{{ad}}" class="data-v-8e954d49" u-i="8e954d49-10" bind:__l="__l" u-p="{{ad}}"/></view></scroll-view><view wx:if="{{ah}}" class="filter-bar calendar-filter-bar sticky-filter-bar data-v-8e954d49"><view class="filter-main-buttons data-v-8e954d49"><view class="filter-button data-v-8e954d49" bindtap="{{al}}"><text class="filter-text data-v-8e954d49">{{ai}}</text><up-icon wx:if="{{ak}}" class="{{['data-v-8e954d49', aj && 'rotate-180']}}" u-i="8e954d49-11" bind:__l="__l" u-p="{{ak}}"></up-icon></view><view class="filter-button data-v-8e954d49" bindtap="{{ap}}"><text class="filter-text data-v-8e954d49">{{am}}</text><up-icon wx:if="{{ao}}" class="{{['data-v-8e954d49', an && 'rotate-180']}}" u-i="8e954d49-12" bind:__l="__l" u-p="{{ao}}"></up-icon></view><view class="filter-button filter-placeholder data-v-8e954d49"><text class="filter-text data-v-8e954d49"></text></view><view class="filter-button filter-placeholder data-v-8e954d49"><text class="filter-text data-v-8e954d49"></text></view></view><view wx:if="{{aq}}" class="filter-panel data-v-8e954d49"><view class="option-grid data-v-8e954d49" style="margin-bottom:20rpx"><view class="{{['data-v-8e954d49', 'option-item', as]}}" bindtap="{{at}}"><text class="option-text data-v-8e954d49">{{ar}}</text></view></view><text class="section-title data-v-8e954d49">热门地区</text><view class="option-grid data-v-8e954d49"><view wx:for="{{av}}" wx:for-item="option" wx:key="b" class="{{['data-v-8e954d49', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-8e954d49">{{option.a}}</text></view></view><view wx:if="{{aw}}" class="data-v-8e954d49"><text class="section-title data-v-8e954d49">其他地区</text><view class="option-grid data-v-8e954d49"><view wx:for="{{ax}}" wx:for-item="city" wx:key="b" class="{{['data-v-8e954d49', 'option-item', city.c]}}" bindtap="{{city.d}}"><text class="option-text data-v-8e954d49">{{city.a}}</text></view></view></view><view class="filter-buttons data-v-8e954d49"><view class="filter-btn reset-btn data-v-8e954d49" bindtap="{{ay}}"><text class="btn-text data-v-8e954d49">重置</text></view><view class="filter-btn complete-btn data-v-8e954d49" bindtap="{{az}}"><text class="btn-text data-v-8e954d49">完成</text></view></view></view><view wx:if="{{aA}}" class="filter-panel data-v-8e954d49"><text class="section-title data-v-8e954d49">时间</text><view class="option-grid data-v-8e954d49"><view wx:for="{{aB}}" wx:for-item="option" wx:key="b" class="{{['data-v-8e954d49', 'option-item', option.c]}}" bindtap="{{option.d}}"><text class="option-text data-v-8e954d49">{{option.a}}</text></view></view><view class="filter-buttons data-v-8e954d49"><view class="filter-btn reset-btn data-v-8e954d49" bindtap="{{aC}}"><text class="btn-text data-v-8e954d49">重置</text></view><view class="filter-btn complete-btn data-v-8e954d49" bindtap="{{aD}}"><text class="btn-text data-v-8e954d49">完成</text></view></view></view></view><scroll-view wx:if="{{aE}}" scroll-y class="event-list-scroll calendar-view calendar-scroll-with-filter data-v-8e954d49" bindscrolltolower="{{aN}}" refresher-enabled refresher-triggered="{{aO}}" bindrefresherrefresh="{{aP}}"><view wx:if="{{aF}}" class="empty-state data-v-8e954d49"><up-empty wx:if="{{aG}}" class="data-v-8e954d49" u-i="8e954d49-13" bind:__l="__l" u-p="{{aG}}"></up-empty><view wx:if="{{aH}}" class="retry-container data-v-8e954d49"><up-button wx:if="{{aJ}}" class="data-v-8e954d49" u-s="{{['d']}}" bindclick="{{aI}}" u-i="8e954d49-14" bind:__l="__l" u-p="{{aJ}}"> 重新加载 </up-button></view></view><event-calendar-timeline wx:else class="data-v-8e954d49" bindclickItem="{{aK}}" bindviewMore="{{aL}}" u-i="8e954d49-15" bind:__l="__l" u-p="{{aM||''}}"/><view class="calendar-bottom-safe data-v-8e954d49"/></scroll-view><custom-tab-bar wx:if="{{aQ}}" class="data-v-8e954d49" u-i="8e954d49-16" bind:__l="__l" u-p="{{aQ}}"/></view>