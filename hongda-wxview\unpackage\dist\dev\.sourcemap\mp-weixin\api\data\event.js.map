{"version": 3, "file": "event.js", "sources": ["api/data/event.js"], "sourcesContent": ["/**\n * 活动相关API接口\n */\nimport http from '@/utils/request.js';\nimport { API_PATHS } from '@/utils/config.js';\n\n/**\n * 获取活动列表\n * @param {Object} params 查询参数\n * @param {number} params.pageNum 页码\n * @param {number} params.pageSize 每页数量\n * @param {string} params.title 活动标题（搜索）\n * @param {string} params.location 活动地点\n * @param {number} params.status 活动状态\n * @param {string} params.startTime 开始时间\n * @param {string} params.endTime 结束时间\n * @param {string} params.orderBy 排序字段\n * @param {string} params.isAsc 是否升序\n * @param {number} params.isHot 是否热门 (1: 热门, 0: 非热门)\n * @returns {Promise} API响应\n */\nexport const getEventListApi = (params) => {\n  return http.get(API_PATHS.EVENT_LIST, params);\n};\n\n/**\n * 获取活动详情\n * @param {number} id 活动ID\n * @returns {Promise} API响应\n */\nexport const getEventDetailApi = (id) => {\n  return http.get(`${API_PATHS.EVENT_DETAIL}/${id}`);\n};\n\n/**\n * 获取热门活动列表（首页用）\n * @param {number} limit 限制数量，默认5条\n * @returns {Promise} API响应\n */\nexport const getHotEventListApi = (limit = 5) => {\n  return http.get(API_PATHS.EVENT_LIST, { \n    pageNum: 1, \n    pageSize: limit, \n    isHot: 1,\n    status: 1 // 只获取报名中的热门活动\n  });\n};\n\n/**\n * 获取活动地区列表（用于筛选下拉框）\n * @returns {Promise} API响应\n */\nexport const getEventLocationsApi = () => {\n  return http.get(API_PATHS.EVENT_LOCATIONS);\n};\n\n/**\n * 获取活动城市列表（用于地区筛选）\n * @returns {Promise} API响应\n */\nexport const getEventCitiesApi = () => {\n  return http.get('/events/cities');\n};\n\n/**\n * 获取即将开始的活动列表\n * @param {number} limit 限制数量，默认10条\n * @returns {Promise} API响应\n */\nexport const getUpcomingEventsApi = (limit = 10) => {\n  const now = new Date();\n  const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);\n  \n  return http.get(API_PATHS.EVENT_LIST, {\n    pageNum: 1,\n    pageSize: limit,\n    status: 1, // 报名中\n    startTime: now.toISOString(),\n    endTime: nextWeek.toISOString(),\n    orderBy: 'startTime',\n    isAsc: 'asc'\n  });\n};\n\n/**\n * 按地区获取活动列表\n * @param {string} location 地区名称\n * @param {Object} options 其他选项\n * @returns {Promise} API响应\n */\nexport const getEventsByLocationApi = (location, options = {}) => {\n  const params = {\n    pageNum: 1,\n    pageSize: 20,\n    location,\n    status: 1, // 默认只获取报名中的活动\n    ...options\n  };\n  \n  return http.get(API_PATHS.EVENT_LIST, params);\n};\n\n/**\n * 搜索活动\n * @param {Object} params 查询参数, e.g., { pageNum, pageSize, title, ... }\n * @returns {Promise} API响应\n */\nexport const searchEventsApi = (params) => {\n  // 直接将页面传递过来的参数对象透传给API请求\n  return http.get(API_PATHS.EVENT_LIST, params);\n};\n\n/**\n * 获取日历视图活动列表\n * 专门为日历视图优化：只返回状态0/1的活动，按开始时间从近到远排序\n * @param {Object} params 查询参数\n * @param {number} params.pageNum 页码\n * @param {number} params.pageSize 每页数量\n * @param {string} params.title 活动标题（搜索）\n * @param {string} params.location 活动地点\n * @param {string} params.timeRangeStart 时间范围开始\n * @param {string} params.timeRangeEnd 时间范围结束\n * @returns {Promise} API响应\n */\nexport const getCalendarEventsApi = (params) => {\n  return http.get('/events/calendar', params);\n};\n\n/**\n * 导出活动列表\n * @param {Object} params 查询参数\n * @returns {Promise} API响应\n */\nexport const exportEventListApi = (params) => {\n  return http.post(API_PATHS.EVENT_EXPORT, params, {\n    responseType: 'blob'\n  });\n}; "], "names": ["http", "API_PATHS"], "mappings": ";;;AAqBY,MAAC,kBAAkB,CAAC,WAAW;AACzC,SAAOA,cAAI,KAAC,IAAIC,aAAS,UAAC,YAAY,MAAM;AAC9C;AAOY,MAAC,oBAAoB,CAAC,OAAO;AACvC,SAAOD,cAAI,KAAC,IAAI,GAAGC,aAAAA,UAAU,YAAY,IAAI,EAAE,EAAE;AACnD;AAOY,MAAC,qBAAqB,CAAC,QAAQ,MAAM;AAC/C,SAAOD,mBAAK,IAAIC,aAAS,UAAC,YAAY;AAAA,IACpC,SAAS;AAAA,IACT,UAAU;AAAA,IACV,OAAO;AAAA,IACP,QAAQ;AAAA;AAAA,EACZ,CAAG;AACH;AAcY,MAAC,oBAAoB,MAAM;AACrC,SAAOD,cAAI,KAAC,IAAI,gBAAgB;AAClC;AA6CY,MAAC,kBAAkB,CAAC,WAAW;AAEzC,SAAOA,cAAI,KAAC,IAAIC,aAAS,UAAC,YAAY,MAAM;AAC9C;AAcY,MAAC,uBAAuB,CAAC,WAAW;AAC9C,SAAOD,mBAAK,IAAI,oBAAoB,MAAM;AAC5C;;;;;;;"}