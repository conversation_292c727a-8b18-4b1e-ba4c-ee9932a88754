<script>
// 1. 导入API方法
import { getAllAssets } from '@/api/platform/asset.js';

export default {
  onLaunch: function () {
    console.log('App Launch')

    // 2. 在App启动时，获取、缓存并加载动态资源
    this.fetchAndCacheAssets();

    // 保留uview-plus图标字体的加载
    this.loadIconFont();
  },
  onShow: function () {
    console.log('App Show')
  },
  onHide: function () {
    console.log('App Hide')
  },
  methods: {
    /**
     * 获取、缓存静态资源，并加载动态字体
     */
    async fetchAndCacheAssets() {
      try {
        const response = await getAllAssets();
        if (response.code === 200 && Array.isArray(response.data)) {
          const assetMap = response.data.reduce((map, item) => {
            if (item.assetKey && item.assetUrl) {
              map[item.assetKey] = item.assetUrl;
            }
            return map;
          }, {});

          uni.setStorageSync('staticAssets', assetMap);
          console.log('小程序静态资源已更新并缓存成功！', assetMap);

          // 缓存成功后，立即尝试加载动态字体
          this.loadDynamicFonts(assetMap);
        }
      } catch (error) {
        console.error('获取小程序静态资源失败', error);
      }
    },

    /**
     * 加载从后台配置的动态字体
     * @param {object} assets - 从缓存中获取的资源对象
     */
    loadDynamicFonts(assets) {
      // 定义我们要加载的字体信息
      const fontToLoad = {
        // 【关键修正】这个 key 必须与您在后台设置的“资源唯一键”完全一致
        key: 'Puhuiti_fonts',
        // 这是我们在CSS中将要使用的字体名称
        family: 'Alibaba PuHuiTi 3.0',
        // 字体粗细 (根据您上传的字体文件设定)
        weight: '400'
      };

      // 检查资源对象中是否存在我们需要的字体
      if (assets && assets[fontToLoad.key]) {
        const fontUrl = assets[fontToLoad.key];

        // 调用 uni.loadFontFace API 进行加载
        uni.loadFontFace({
          global: true, // 设置为全局字体
          family: fontToLoad.family,
          source: `url("${fontUrl}")`,
          desc: {
            weight: fontToLoad.weight
          },
          success() {
            console.log(`动态字体 [${fontToLoad.family}] 加载成功!`);
          },
          fail(err) {
            console.error(`动态字体 [${fontToLoad.family}] 加载失败:`, err);
          }
        });
      } else {
        console.warn(`未在资源配置中找到字体资源: ${fontToLoad.key}`);
      }
    },

    /**
     * 加载uView图标字体 (保留)
     */
    loadIconFont() {
      // #ifdef APP || H5 || MP-WEIXIN || MP-ALIPAY
      uni.loadFontFace({
        global: true,
        family: 'uicon-iconfont',
        source: 'url("https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf")',
        success() {
          console.log('uview-plus图标字体加载成功');
        },
        fail(err) {
          console.error('uview-plus图标字体加载失败:', err);
          uni.loadFontFace({
            global: true,
            family: 'uicon-iconfont',
            source: 'url("https://cdn.jsdelivr.net/npm/uview-plus@3.1.37/fonts/uicon-iconfont.ttf")',
            success() {
              console.log('备用图标字体加载成功');
            },
            fail(err2) {
              console.error('备用图标字体也加载失败:', err2);
            }
          });
        }
      });
      // #endif
    },

    /**
     * 【已移除】loadPuHuiTiFonts 方法已被移除，因为字体现在是动态加载的
     */
  }
}
</script>

<style lang="scss">
/* 每个页面公共css */
@import "@/uni_modules/uview-plus/index.scss";

/* 防止页面跳转时内容残留 */
uni-page-wrapper {
  overflow: hidden !important;
}
uni-page-body {
  overflow: hidden !important;
}

/* 仅用于测试 */
body, page, view, text {
  font-family: 'Alibaba PuHuiTi 3.0', sans-serif;
}
</style>
